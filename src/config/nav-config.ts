import {
  Video,
  Settings,
  FileVideo,
  CreditCard,
  Lightbulb,
  Link,
  FileText,
  FileUp,
  AudioLines,
  Radio,
  FolderKanban,
} from 'lucide-react'
import { LucideIcon } from 'lucide-react'

export interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: NavItem[]
}

export const navMain: NavItem[] = [
  {
    title: 'Create Video',
    url: '/',
    icon: Video,
    items: [
      {
        title: 'Idea to Video',
        url: '/create-video/idea',
        icon: Lightbulb,
      },
      {
        title: 'Blog to Video',
        url: '/create-video/blog',
        icon: Link,
      },
      {
        title: 'Text to Video',
        url: '/create-video/text',
        icon: FileText,
      },
      {
        title: 'PDF to Video',
        url: '/create-video/pdf',
        icon: FileUp,
      },
      {
        title: 'Audio to Video',
        url: '/create-video/audio',
        icon: AudioLines,
      },
      {
        title: 'Podcast to Video',
        url: '/create-video/podcast',
        icon: Radio,
      },
    ],
  },
  {
    title: 'My Projects',
    url: '/projects',
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    items: [],
  },
  {
    title: 'My Videos',
    url: '/my-videos',
    icon: FileVideo,
    items: [],
  },
  {
    title: 'Billing',
    url: '/billing',
    icon: CreditCard,
    items: [],
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: Settings,
    items: [],
  },
]
