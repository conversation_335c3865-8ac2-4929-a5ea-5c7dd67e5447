'use client'

import { useRouter, usePathname } from 'next/navigation'
import { useCallback, useEffect, useRef } from 'react'

/**
 * Hook for strategic page prefetching to optimize dashboard navigation performance
 * Focuses on Next.js router.prefetch() for page-level optimization
 */
export function usePagePrefetch() {
  const router = useRouter()
  const pathname = usePathname()
  const prefetchedPages = useRef(new Set<string>())
  const hoverTimeouts = useRef(new Map<string, NodeJS.Timeout>())

  // Core dashboard pages that should be prefetched immediately
  const CORE_PAGES = useRef(['/my-videos', '/projects', '/pricing']).current

  // All create-video pages for batch prefetching
  const CREATE_VIDEO_PAGES = useRef([
    '/create-video/idea',
    '/create-video/blog',
    '/create-video/text',
    '/create-video/pdf',
    '/create-video/audio',
    '/create-video/podcast',
  ]).current

  /**
   * Prefetch a page if not already prefetched
   */
  const prefetchPage = useCallback(
    (url: string) => {
      if (!prefetchedPages.current.has(url) && router?.prefetch) {
        router.prefetch(url)
        prefetchedPages.current.add(url)
        console.log(
          `🚀 Prefetched: ${url} - This helps with RSC payload, not initial bundle loading`
        )
      }
    },
    [router]
  )

  /**
   * Background prefetching of core dashboard pages
   */
  const backgroundPrefetch = useCallback(() => {
    // Prefetch core pages immediately
    CORE_PAGES.forEach(page => {
      prefetchPage(page)
    })

    // Prefetch main create-video page
    prefetchPage('/create-video')
  }, [prefetchPage, CORE_PAGES])

  /**
   * Prefetch all create-video pages (used when user shows interest in video creation)
   */
  const prefetchVideoCreationPages = useCallback(() => {
    CREATE_VIDEO_PAGES.forEach(page => {
      prefetchPage(page)
    })
  }, [prefetchPage, CREATE_VIDEO_PAGES])

  /**
   * Context-aware prefetching based on current route
   */
  const contextualPrefetch = useCallback(() => {
    if (pathname === '/') {
      // On dashboard, prefetch most likely next destinations
      prefetchPage('/create-video/idea') // Most popular creation method
    } else if (pathname.startsWith('/create-video')) {
      // In create-video section, prefetch my-videos (to see results)
      prefetchPage('/my-videos')
    } else if (pathname === '/my-videos') {
      // From my-videos, users often go to projects or create more
      prefetchPage('/projects')
      prefetchPage('/create-video/idea')
    } else if (pathname === '/projects') {
      // From projects, users often check their videos
      prefetchPage('/my-videos')
    }
  }, [pathname, prefetchPage])

  /**
   * Debounced hover prefetching to avoid excessive requests
   */
  const prefetchOnHover = useCallback(
    (url: string) => {
      // Clear existing timeout for this URL
      const existingTimeout = hoverTimeouts.current.get(url)
      if (existingTimeout) {
        clearTimeout(existingTimeout)
      }

      // Set new timeout for prefetching
      const timeout = setTimeout(() => {
        prefetchPage(url)
        hoverTimeouts.current.delete(url)
      }, 100) // 100ms delay to avoid excessive prefetching

      hoverTimeouts.current.set(url, timeout)
    },
    [prefetchPage]
  )

  /**
   * Enhanced hover prefetching with related pages
   */
  const prefetchOnHoverWithContext = useCallback(
    (url: string) => {
      // Always prefetch the hovered page
      prefetchOnHover(url)

      // Prefetch related pages based on the hovered URL
      if (url === '/create-video' || url === '/') {
        // If hovering create-video section, prefetch video creation pages
        setTimeout(() => prefetchVideoCreationPages(), 200)
      } else if (url === '/my-videos') {
        // If hovering my-videos, prefetch projects (related content)
        setTimeout(() => prefetchPage('/projects'), 200)
      } else if (url === '/projects') {
        // If hovering projects, prefetch my-videos
        setTimeout(() => prefetchPage('/my-videos'), 200)
      }
    },
    [prefetchOnHover, prefetchVideoCreationPages, prefetchPage]
  )

  // Initialize background prefetching on mount
  useEffect(() => {
    // Skip prefetching on auth pages
    if (pathname.startsWith('/signin') || pathname.startsWith('/signup')) {
      return
    }

    // Run background prefetching after a short delay to not block initial render
    const timer = setTimeout(() => {
      backgroundPrefetch()
      contextualPrefetch()
    }, 500)

    return () => clearTimeout(timer)
  }, [backgroundPrefetch, contextualPrefetch, pathname])

  // Contextual prefetching when route changes
  useEffect(() => {
    if (pathname.startsWith('/signin') || pathname.startsWith('/signup')) {
      return
    }

    const timer = setTimeout(() => {
      contextualPrefetch()
    }, 300)

    return () => clearTimeout(timer)
  }, [pathname, contextualPrefetch])

  // Cleanup timeouts on unmount
  useEffect(() => {
    const timeouts = hoverTimeouts.current
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout))
      timeouts.clear()
    }
  }, [])

  // Return no-op functions if router is not available
  if (!router) {
    return {
      prefetchOnHover: () => {},
      prefetchVideoCreationPages: () => {},
      prefetchPage: () => {},
      backgroundPrefetch: () => {},
      contextualPrefetch: () => {},
    }
  }

  return {
    prefetchOnHover: prefetchOnHoverWithContext,
    prefetchVideoCreationPages,
    prefetchPage,
    backgroundPrefetch,
    contextualPrefetch,
  }
}
