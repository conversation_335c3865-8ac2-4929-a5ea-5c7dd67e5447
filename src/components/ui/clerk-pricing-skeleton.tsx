import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'

export function ClerkPricingSkeleton() {
  return (
    <div className='w-full clerk-pricing-transition'>
      {/* Pricing cards grid - matches <PERSON>'s 2x2 layout on desktop */}
      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-4xl mx-auto'>
        {/* Free Card */}
        <Card className='relative border border-border rounded-lg p-4 sm:p-6 bg-white shadow-sm'>
          <div className='space-y-3 sm:space-y-4'>
            {/* Plan name and badge */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <Skeleton className='h-5 w-20' />
                <Skeleton className='h-5 w-12 rounded-full' />
              </div>
            </div>

            {/* Price */}
            <div className='space-y-1'>
              <div className='flex items-baseline gap-1'>
                <Skeleton className='h-8 w-6' />
                <Skeleton className='h-8 w-4' />
              </div>
              <Skeleton className='h-4 w-20' />
            </div>

            {/* Features list */}
            <div className='space-y-2 pt-3 sm:pt-4'>
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className='flex items-center gap-2'>
                  <Skeleton className='h-3 w-3 rounded-full flex-shrink-0' />
                  <Skeleton className='h-3 w-full' />
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Basic Plan Card */}
        <Card className='relative border border-border rounded-lg p-4 sm:p-6 bg-white shadow-sm'>
          <div className='space-y-3 sm:space-y-4'>
            {/* Plan name */}
            <div className='space-y-2'>
              <Skeleton className='h-5 w-16' />
              <Skeleton className='h-4 w-32' />
            </div>

            {/* Price */}
            <div className='space-y-1'>
              <div className='flex items-baseline gap-1'>
                <Skeleton className='h-8 w-8' />
                <Skeleton className='h-8 w-6' />
                <Skeleton className='h-4 w-12' />
              </div>
              <div className='flex items-center gap-2'>
                <Skeleton className='h-4 w-4 rounded-full' />
                <Skeleton className='h-4 w-20' />
              </div>
            </div>

            {/* Features list */}
            <div className='space-y-2 pt-4'>
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className='flex items-center gap-2'>
                  <Skeleton className='h-3 w-3 rounded-full flex-shrink-0' />
                  <Skeleton className='h-3 w-full' />
                </div>
              ))}
            </div>

            {/* Button */}
            <div className='pt-4'>
              <Skeleton className='h-9 w-full rounded-md bg-black/10' />
            </div>
          </div>
        </Card>

        {/* Premium Plan Card */}
        <Card className='relative border border-border rounded-lg p-4 sm:p-6 bg-white shadow-sm'>
          <div className='space-y-3 sm:space-y-4'>
            {/* Plan name */}
            <div className='space-y-2'>
              <Skeleton className='h-5 w-20' />
              <Skeleton className='h-4 w-40' />
            </div>

            {/* Price */}
            <div className='space-y-1'>
              <div className='flex items-baseline gap-1'>
                <Skeleton className='h-8 w-8' />
                <Skeleton className='h-8 w-8' />
                <Skeleton className='h-4 w-12' />
              </div>
              <div className='flex items-center gap-2'>
                <Skeleton className='h-4 w-4 rounded-full' />
                <Skeleton className='h-4 w-24' />
              </div>
            </div>

            {/* Features list */}
            <div className='space-y-2 pt-4'>
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className='flex items-center gap-2'>
                  <Skeleton className='h-3 w-3 rounded-full flex-shrink-0' />
                  <Skeleton className='h-3 w-full' />
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Business Plan Card */}
        <Card className='relative border border-border rounded-lg p-4 sm:p-6 bg-white shadow-sm'>
          <div className='space-y-3 sm:space-y-4'>
            {/* Plan name */}
            <div className='space-y-2'>
              <Skeleton className='h-5 w-20' />
              <Skeleton className='h-4 w-36' />
            </div>

            {/* Price */}
            <div className='space-y-1'>
              <div className='flex items-baseline gap-1'>
                <Skeleton className='h-8 w-8' />
                <Skeleton className='h-8 w-10' />
                <Skeleton className='h-4 w-12' />
              </div>
              <div className='flex items-center gap-2'>
                <Skeleton className='h-4 w-4 rounded-full' />
                <Skeleton className='h-4 w-24' />
              </div>
            </div>

            {/* Features list */}
            <div className='space-y-2 pt-4'>
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className='flex items-center gap-2'>
                  <Skeleton className='h-3 w-3 rounded-full flex-shrink-0' />
                  <Skeleton className='h-3 w-full' />
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

// Compact version for smaller screens
export function ClerkPricingSkeletonCompact() {
  return (
    <div className='w-full'>
      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-4xl mx-auto'>
        {Array.from({ length: 4 }).map((_, index) => (
          <Card
            key={index}
            className='relative border border-border rounded-lg p-4 bg-white shadow-sm'
          >
            <div className='space-y-3'>
              {/* Plan name */}
              <div className='space-y-1'>
                <Skeleton className='h-4 w-16' />
                <Skeleton className='h-3 w-24' />
              </div>

              {/* Price */}
              <div className='flex items-baseline gap-1'>
                <Skeleton className='h-6 w-6' />
                <Skeleton className='h-6 w-8' />
                <Skeleton className='h-3 w-10' />
              </div>

              {/* Features */}
              <div className='space-y-1.5 pt-2'>
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className='flex items-center gap-2'>
                    <Skeleton className='h-2.5 w-2.5 rounded-full' />
                    <Skeleton className='h-2.5 flex-1' />
                  </div>
                ))}
              </div>

              {/* Button */}
              <Skeleton className='h-8 w-full rounded-md' />
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}
