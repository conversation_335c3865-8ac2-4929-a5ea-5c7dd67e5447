'use client'
import React from 'react'

// Import from backup pricing components
import {
  BillingToggle,
  PricingTable,
} from '@/components/backup/pricing-original/_components'
import { BadgePercent, ArrowRight } from 'lucide-react'
import { ModalData } from '@/types/modal'
import { Plan, BillingPeriod } from '@/types/pricing'

// Plan data
const plans: Plan[] = [
  {
    name: 'Free',
    price: '$0',
    billingPeriod: '/forever',
    description: 'Try Adori AI with basic features',
    highlightTag: null,
    videoCreation: 1,
    videoDownloads: 0,
    maxVideoLength: '1 minute',
    resolution: 'HD',
    aiImageGenerator: '20 images',
    publishToYoutube: false,
    proVoices: false,
    voiceCloning: false,
    curationSupport: false,
    buttonText: 'Get started',
    buttonVariant: 'outline' as const,
  },
  {
    name: 'Basic',
    price: '$12',
    annualPrice: '$144',
    annualSavings: 'Save $36',
    billingPeriod: 'billed annually',
    description: 'Ideal for individuals and small creators',
    highlightTag: null,
    videoCreation: 120,
    videoDownloads: 240,
    maxVideoLength: '3 minutes',
    resolution: 'HD',
    aiImageGenerator: '1.2k images',
    publishToYoutube: false,
    proVoices: false,
    voiceCloning: false,
    curationSupport: false,
    buttonText: 'Get started',
    buttonVariant: 'default' as const,
  },
  {
    name: 'Premium',
    price: '$36',
    annualPrice: '$432',
    annualSavings: 'Save $108',
    billingPeriod: 'billed annually',
    description: 'Perfect for professional creators',
    highlightTag: 'Most Popular',
    videoCreation: 240,
    videoDownloads: 480,
    maxVideoLength: '5 minutes',
    resolution: 'Full HD',
    aiImageGenerator: '3.6k images',
    publishToYoutube: true,
    proVoices: true,
    voiceCloning: false,
    curationSupport: false,
    buttonText: 'Get started',
    buttonVariant: 'default' as const,
  },
  {
    name: 'Business',
    price: '$159',
    annualPrice: '$1900',
    annualSavings: 'Save $488',
    billingPeriod: 'billed annually',
    description: 'For teams and businesses',
    highlightTag: null,
    videoCreation: 600,
    videoDownloads: 1200,
    maxVideoLength: '10 minutes',
    resolution: 'Full HD',
    aiImageGenerator: '12k images',
    publishToYoutube: true,
    proVoices: true,
    voiceCloning: true,
    curationSupport: '30 minutes per month',
    buttonText: 'Contact us',
    buttonVariant: 'outline' as const,
  },
]

interface PricingModalProps extends ModalData {
  initialBillingPeriod?: BillingPeriod
}

export function PricingModal({
  initialBillingPeriod = 'annual',
}: PricingModalProps = {}) {
  const [billingPeriod, setBillingPeriod] =
    React.useState<BillingPeriod>(initialBillingPeriod)

  return (
    <div className='max-w-7xl mx-auto py-6 px-2 sm:px-4'>
      {/* Page header */}
      <div className='mb-8'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6'>
          <div className='text-center sm:text-left mb-6 sm:mb-0'>
            <h1 className='text-2xl font-bold mb-2'>Choose your plan</h1>
            <p className='text-muted-foreground max-w-xl text-sm'>
              Select the perfect plan for your content creation needs. All plans
              include access to our AI video creation tools.
            </p>
          </div>

          {/* Billing Toggle */}
          <div className='flex justify-center sm:justify-end'>
            <BillingToggle
              activePeriod={billingPeriod}
              onChange={setBillingPeriod}
            />
          </div>
        </div>

        {/* Annual savings callout */}
        {billingPeriod === 'annual' && (
          <div className='bg-green-500/10 text-green-600 dark:text-green-400 text-sm px-4 py-2 rounded-lg inline-flex items-center gap-1.5 mx-auto sm:mx-0'>
            <BadgePercent className='h-4 w-4' />
            Save up to 25% with annual billing
          </div>
        )}
      </div>

      {/* Pricing Table */}
      <div className='mb-12'>
        <PricingTable plans={plans} billingPeriod={billingPeriod} />
      </div>

      {/* Enterprise Contact Section */}
      <div className='w-full mx-auto rounded-lg bg-card border border-border/40 p-6 text-center'>
        <div className='max-w-lg mx-auto'>
          <h2 className='text-lg font-semibold mb-2'>
            Need a custom solution?
          </h2>
          <p className='text-muted-foreground mb-4 text-sm'>
            Our team can help you find the perfect plan for your content
            creation needs. We offer custom solutions for enterprise customers
            with special requirements.
          </p>
          <a
            href='mailto:<EMAIL>'
            className='inline-flex items-center gap-1.5 text-primary hover:text-primary/90 font-medium'
          >
            Contact our sales team
            <ArrowRight className='h-4 w-4' />
          </a>
        </div>
      </div>
    </div>
  )
}
