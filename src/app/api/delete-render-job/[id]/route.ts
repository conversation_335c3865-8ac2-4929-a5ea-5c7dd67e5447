import { NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { renderJobs } from '@/db/schema'
import { eq } from 'drizzle-orm'
// import { getAuth } from '@clerk/nextjs/server'
import { getUserSession } from '@/lib/user-utils'
import { getActiveOrganizationForUser } from '@/lib/organization-utils'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getUserSession()
    // const { userId } = getAuth(request)
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 })
    }
    const userId = session?.user?.id

    const { id: jobId } = await params

    // Get the user's active organization
    const activeOrganization = await getActiveOrganizationForUser(userId)

    // Check if render job exists and user has access to it
    const jobs = await db
      .select()
      .from(renderJobs)
      .where(eq(renderJobs.id, jobId))

    if (!jobs.length) {
      return new Response('Render job not found', { status: 404 })
    }

    const job = jobs[0]

    // Check if user has access to this render job
    let hasAccess = false

    if (activeOrganization) {
      // If user is in an organization, they can delete render jobs from that organization
      hasAccess = job.organizationId === activeOrganization.organizationId
    } else {
      // If user is not in an organization, they can only delete their own render jobs
      hasAccess = job.userId === userId
    }

    if (!hasAccess) {
      return new Response(
        'Forbidden: You do not have permission to delete this render job',
        { status: 403 }
      )
    }

    await db.delete(renderJobs).where(eq(renderJobs.id, jobId))
    return new Response('Video deleted', { status: 200 })
  } catch (error) {
    console.error('Delete video error:', error)
    return new Response('Delete failed', { status: 500 })
  }
}
