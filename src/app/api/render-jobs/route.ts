import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase-client'
import { getUserSession } from '@/lib/user-utils'
import { getActiveOrganizationForUser } from '@/lib/organization-utils'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from Clerk
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      console.error('❌ Unauthorized access attempt to render jobs API')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    // Get the user's active organization
    const activeOrganization = await getActiveOrganizationForUser(userId)

    console.log('🔧 API: Fetching render jobs:', {
      userId,
      projectId,
      organizationId: activeOrganization?.organizationId,
    })

    try {
      // Build query using the existing client-side supabase instance
      let query = supabase
        .from('render_jobs')
        .select('*')
        .order('created_at', { ascending: false })

      // Apply filtering based on organization membership
      if (activeOrganization) {
        // If user is in an organization, show render jobs from that organization
        query = query.eq('organization_id', activeOrganization.organizationId)
      } else {
        // If user is not in an organization, show only their own render jobs
        query = query.eq('user_id', userId)
      }

      if (projectId) {
        query = query.eq('project_id', projectId)
      }

      const { data, error } = await query

      if (error) {
        console.error('❌ Database error in render jobs API:', error)
        return NextResponse.json(
          { error: 'Database error', details: error.message },
          { status: 500 }
        )
      }

      console.log(
        '✅ API: Successfully fetched render jobs:',
        (data || []).length
      )

      return NextResponse.json({
        data: data || [],
        success: true,
      })
    } catch (dbError) {
      console.error('❌ Database query error:', dbError)
      return NextResponse.json(
        { error: 'Database query failed' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('❌ API error in render jobs route:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
