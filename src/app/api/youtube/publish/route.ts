import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { getUserSession } from '@/lib/user-utils'
import { db } from '@/lib/db'
import { renderJobs, youtubeConnections } from '@/db/schema'
import { eq, and } from 'drizzle-orm'
import { google } from 'googleapis'
import {
  createOAuth2Client,
  ensureValidAccessToken,
  canUploadCustomThumbnails,
} from '@/lib/youtube-auth'
import { Readable } from 'stream'

interface PublishRequest {
  renderJobId: string
  connectionId: string
  metadata: {
    title: string
    description: string
    privacy: 'public' | 'unlisted' | 'private'
    categoryId: string
    tags: string[]
    madeForKids: boolean
    playlistId?: string
    thumbnailUrl?: string
  }
}

export async function POST(request: NextRequest) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()
    const userId = session?.user?.id
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: PublishRequest = await request.json()
    const { renderJobId, connectionId, metadata } = body

    // Validate required fields
    if (!renderJobId || !connectionId || !metadata.title) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get the render job
    const renderJob = await db
      .select()
      .from(renderJobs)
      .where(and(eq(renderJobs.id, renderJobId), eq(renderJobs.userId, userId)))
      .limit(1)

    if (renderJob.length === 0) {
      return NextResponse.json(
        { error: 'Render job not found' },
        { status: 404 }
      )
    }

    const job = renderJob[0]

    // Check if job is completed and has a public URL
    if (job.status !== 'completed' || !job.publicUrl) {
      return NextResponse.json(
        { error: 'Video is not ready for publishing' },
        { status: 400 }
      )
    }

    // Check if already published
    if (job.youtubeId) {
      return NextResponse.json(
        { error: 'Video is already published to YouTube' },
        { status: 400 }
      )
    }

    // Get the YouTube connection
    const connection = await db
      .select()
      .from(youtubeConnections)
      .where(
        and(
          eq(youtubeConnections.id, connectionId),
          eq(youtubeConnections.userId, userId),
          eq(youtubeConnections.isActive, true)
        )
      )
      .limit(1)

    if (connection.length === 0) {
      return NextResponse.json(
        { error: 'YouTube connection not found' },
        { status: 404 }
      )
    }

    const conn = connection[0]

    // Ensure we have a valid access token (refresh if necessary)
    let accessToken: string
    try {
      accessToken = await ensureValidAccessToken(conn)
    } catch (error) {
      // Handle connection revoked error specifically
      if (
        error instanceof Error &&
        error.message === 'YOUTUBE_CONNECTION_REVOKED'
      ) {
        return NextResponse.json(
          {
            error:
              'YouTube connection has been revoked. Please reconnect your account.',
            code: 'CONNECTION_REVOKED',
          },
          { status: 410 } // 410 Gone - resource no longer available
        )
      }

      // Handle other refresh errors
      return NextResponse.json(
        {
          error:
            error instanceof Error
              ? error.message
              : 'Failed to refresh YouTube connection',
        },
        { status: 401 }
      )
    }

    // Set up YouTube API client
    const oauth2Client = createOAuth2Client()
    oauth2Client.setCredentials({ access_token: accessToken })

    const youtube = google.youtube({ version: 'v3', auth: oauth2Client })

    // Download the video file
    console.log('Downloading video from:', job.publicUrl)
    const videoResponse = await fetch(job.publicUrl)
    if (!videoResponse.ok) {
      throw new Error('Failed to download video file')
    }

    // Convert the response to a readable stream
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const videoStream = Readable.fromWeb(videoResponse.body as any)

    // Prepare video metadata
    const videoMetadata = {
      snippet: {
        title: metadata.title,
        description: metadata.description,
        tags: metadata.tags,
        categoryId: metadata.categoryId,
        defaultLanguage: 'en',
        defaultAudioLanguage: 'en',
      },
      status: {
        privacyStatus: metadata.privacy,
        madeForKids: metadata.madeForKids,
        selfDeclaredMadeForKids: metadata.madeForKids,
      },
    }

    console.log('Uploading video to YouTube...')

    // Upload video to YouTube
    const uploadResponse = await youtube.videos.insert({
      part: ['snippet', 'status'],
      requestBody: videoMetadata,
      media: {
        body: videoStream,
      },
    })

    const youtubeId = uploadResponse.data.id
    if (!youtubeId) {
      throw new Error('Failed to get YouTube video ID from upload response')
    }

    console.log('Video uploaded successfully. YouTube ID:', youtubeId)

    // Upload custom thumbnail if provided
    if (metadata.thumbnailUrl) {
      try {
        console.log('Checking thumbnail upload permissions...')

        // Check if the channel can upload custom thumbnails
        const canUploadThumbnails = await canUploadCustomThumbnails(accessToken)
        if (!canUploadThumbnails) {
          console.warn(
            'Channel does not have custom thumbnail upload permissions. Skipping thumbnail upload.'
          )
          console.warn(
            'To enable custom thumbnails: 1) Verify your channel, 2) Ensure no recent Community Guidelines strikes'
          )
        } else {
          console.log('Uploading custom thumbnail:', metadata.thumbnailUrl)

          // Fetch the thumbnail image
          const thumbnailResponse = await fetch(metadata.thumbnailUrl)
          if (!thumbnailResponse.ok) {
            throw new Error('Failed to fetch thumbnail image')
          }

          // Convert the response to a readable stream (same fix as video upload)
          const thumbnailStream = Readable.fromWeb(
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            thumbnailResponse.body as any
          )

          // Upload thumbnail to YouTube
          await youtube.thumbnails.set({
            videoId: youtubeId,
            media: {
              body: thumbnailStream,
            },
          })

          console.log('Custom thumbnail uploaded successfully')
        }
      } catch (thumbnailError) {
        console.error('Failed to upload custom thumbnail:', thumbnailError)

        // Provide specific error messages for common issues
        if (thumbnailError instanceof Error) {
          if (thumbnailError.message.includes('permissions')) {
            console.error(
              'Thumbnail upload failed: Channel lacks custom thumbnail permissions'
            )
            console.error(
              'Solution: Verify your YouTube channel and ensure no recent Community Guidelines strikes'
            )
          } else if (thumbnailError.message.includes('403')) {
            console.error(
              'Thumbnail upload failed: Forbidden - check channel verification status'
            )
          }
        }

        // Don't fail the entire upload if thumbnail upload fails
        // The video was uploaded successfully, thumbnail is optional
      }
    }

    // Add video to playlist if specified
    if (metadata.playlistId) {
      try {
        console.log('Adding video to playlist:', metadata.playlistId)
        await youtube.playlistItems.insert({
          part: ['snippet'],
          requestBody: {
            snippet: {
              playlistId: metadata.playlistId,
              resourceId: {
                kind: 'youtube#video',
                videoId: youtubeId,
              },
            },
          },
        })
        console.log('Video added to playlist successfully')
      } catch (playlistError) {
        console.error('Failed to add video to playlist:', playlistError)
        // Don't fail the entire upload if playlist assignment fails
      }
    }

    // Update the render job with YouTube ID
    await db
      .update(renderJobs)
      .set({
        youtubeId,
        updatedAt: new Date(),
      })
      .where(eq(renderJobs.id, renderJobId))

    return NextResponse.json({
      success: true,
      youtubeId,
      youtubeUrl: `https://youtube.com/watch?v=${youtubeId}`,
    })
  } catch (error) {
    console.error('YouTube publish error:', error)

    // Handle specific YouTube API errors
    if (error instanceof Error) {
      if (error.message.includes('quotaExceeded')) {
        return NextResponse.json(
          { error: 'YouTube API quota exceeded. Please try again later.' },
          { status: 429 }
        )
      }
      if (error.message.includes('uploadLimitExceeded')) {
        return NextResponse.json(
          { error: 'Daily upload limit exceeded for this channel.' },
          { status: 429 }
        )
      }
      if (error.message.includes('forbidden')) {
        return NextResponse.json(
          {
            error: 'YouTube channel does not have permission to upload videos.',
          },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to publish video to YouTube' },
      { status: 500 }
    )
  }
}
