import { NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { getUserSession } from '@/lib/user-utils'
import { db } from '@/lib/db'
import { youtubeConnections } from '@/db/schema'
import { eq } from 'drizzle-orm'
import { decryptTokens } from '@/lib/encryption'
import { revokeTokens } from '@/lib/youtube-auth'

export async function POST(request: Request) {
  try {
    // Check if user is authenticated
    // const { userId } = await auth()
    const session = await getUserSession()
    const userId = session?.user?.id
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Get connection ID from request body (optional - if not provided, disconnect all)
    const body = await request.json().catch(() => ({}))
    const { connectionId } = body

    let whereClause
    if (connectionId) {
      // Disconnect specific connection
      whereClause = eq(youtubeConnections.id, connectionId)
    } else {
      // Disconnect all connections for user
      whereClause = eq(youtubeConnections.userId, userId)
    }

    // Get YouTube connections from database
    const connections = await db
      .select()
      .from(youtubeConnections)
      .where(whereClause)

    if (!connections.length) {
      return NextResponse.json({
        success: true,
        message: 'No connections found',
      })
    }

    // Delete the connections from database FIRST for immediate UI feedback
    await db.delete(youtubeConnections).where(whereClause)

    const message = connectionId
      ? 'YouTube connection disconnected successfully'
      : `${connections.length} YouTube connection(s) disconnected successfully`

    // Prepare response immediately
    const response = NextResponse.json({
      success: true,
      message,
      disconnectedCount: connections.length,
    })

    // Use setImmediate to ensure token revocation runs truly in background
    // This completely detaches the background work from the HTTP response
    setImmediate(() => {
      Promise.allSettled(
        connections.map(async conn => {
          try {
            const tokens = decryptTokens({
              encryptedAccessToken: conn.accessToken,
              encryptedRefreshToken: conn.refreshToken,
            })
            await revokeTokens(tokens.access_token)
            console.log(
              `Successfully revoked tokens for connection: ${conn.id}`
            )
          } catch (error) {
            console.error(
              'Failed to revoke YouTube tokens for connection:',
              conn.id,
              error
            )
          }
        })
      ).catch(error => {
        console.error('Background token revocation failed:', error)
      })
    })

    return response
  } catch (error) {
    console.error('Error disconnecting YouTube:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
