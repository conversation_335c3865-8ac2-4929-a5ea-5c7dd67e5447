import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'
import { db } from '@/lib/db'
import { mediaAssets } from '@/db/schema'
import { eq, like, ilike, desc, and, count, or } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'
// Server-side processing removed - using client-side thumbnail generation

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Define allowed media types
const ALLOWED_MEDIA_TYPES = {
  // Images
  'image/jpeg': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'JPEG Image',
  },
  'image/jpg': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'JPG Image',
  },
  'image/png': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'PNG Image',
  },
  'image/webp': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'WebP Image',
  },
  'image/gif': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'GIF Image',
  },

  // Videos
  'video/mp4': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'MP4 Video',
  },
  'video/webm': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'WebM Video',
  },
  'video/quicktime': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'QuickTime Video',
  },
  'video/x-msvideo': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'AVI Video',
  },
}

export async function POST(request: NextRequest) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check if file type is allowed
    const fileConfig =
      ALLOWED_MEDIA_TYPES[file.type as keyof typeof ALLOWED_MEDIA_TYPES]
    if (!fileConfig) {
      return NextResponse.json(
        {
          error: `Invalid file type. Allowed types: ${Object.keys(
            ALLOWED_MEDIA_TYPES
          )
            .map(
              type =>
                ALLOWED_MEDIA_TYPES[type as keyof typeof ALLOWED_MEDIA_TYPES]
                  .label
            )
            .join(', ')}`,
        },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > fileConfig.maxSize) {
      const maxSizeMB = fileConfig.maxSize / (1024 * 1024)
      return NextResponse.json(
        { error: `File too large. Maximum size is ${maxSizeMB}MB.` },
        { status: 400 }
      )
    }

    // Determine file type and folder
    const isImage = file.type.startsWith('image/')
    const folder = isImage ? 'images' : 'videos'

    // Basic metadata (will be enhanced by client-side processing)
    const extractedMetadata = {
      width: 0,
      height: 0,
      duration: undefined as number | undefined,
      fileSize: file.size,
      mimeType: file.type,
      aspectRatio: 1,
      orientation: 'square' as const,
      quality: undefined as string | undefined,
    }
    const dominantColors: string[] = []

    // Create file path
    const timestamp = Date.now()
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
    const filePath = `${userId}/${folder}/${timestamp}-${sanitizedFileName}`

    // Upload original file to Supabase
    const { error: uploadError } = await supabase.storage
      .from(fileConfig.bucket)
      .upload(filePath, file, {
        upsert: false,
        contentType: file.type,
      })

    if (uploadError) {
      console.error('Supabase upload error:', uploadError)
      return NextResponse.json(
        { error: 'Failed to upload file' },
        { status: 500 }
      )
    }

    // Get the public URL for original file
    const { data: originalUrlData } = supabase.storage
      .from(fileConfig.bucket)
      .getPublicUrl(filePath)

    // Handle thumbnail upload if provided by client
    let thumbnailUrl: string | undefined
    const thumbnailFile = formData.get('thumbnail') as File | null

    if (thumbnailFile) {
      try {
        const thumbnailPath = `${userId}/thumbnails/${timestamp}-thumb-${sanitizedFileName.replace(/\.[^/.]+$/, '.jpg')}`

        const { error: thumbError } = await supabase.storage
          .from(fileConfig.bucket)
          .upload(thumbnailPath, thumbnailFile, {
            upsert: false,
            contentType: 'image/jpeg',
          })

        if (!thumbError) {
          const { data: thumbUrlData } = supabase.storage
            .from(fileConfig.bucket)
            .getPublicUrl(thumbnailPath)
          thumbnailUrl = thumbUrlData.publicUrl
        }
      } catch (error) {
        console.warn('Thumbnail upload failed:', error)
        // Continue without thumbnail - not a critical error
      }
    }

    // Quality is already determined in extractedMetadata for videos

    // Save metadata to database
    const mediaRecord = {
      userId,
      fileName: `${timestamp}-${sanitizedFileName}`,
      originalName: file.name,
      mimeType: file.type,
      fileSize: file.size,
      originalUrl: originalUrlData.publicUrl,
      thumbnailUrl,
      width: extractedMetadata.width || null,
      height: extractedMetadata.height || null,
      duration: extractedMetadata.duration
        ? extractedMetadata.duration.toString()
        : null,
      quality: extractedMetadata.quality || null,
      metadata: {
        aspectRatio: extractedMetadata.aspectRatio,
        orientation: extractedMetadata.orientation,
        dominantColors: dominantColors,
        tags: [],
        description: undefined,
        alt: undefined,
      },
    }

    const [insertedRecord] = await db
      .insert(mediaAssets)
      .values(mediaRecord)
      .returning()

    // Return response similar to stock API format
    const response = {
      id: insertedRecord.id,
      width: extractedMetadata.width,
      height: extractedMetadata.height,
      duration: extractedMetadata.duration,
      file_size: file.size,
      quality: extractedMetadata.quality,
      tags: [],
      url: originalUrlData.publicUrl,
      thumbnail: thumbnailUrl,
      user: {
        id: userId,
        name: 'User', // You might want to get actual user name from Clerk
      },
      metadata: {
        aspectRatio: extractedMetadata.aspectRatio,
        orientation: extractedMetadata.orientation,
        dominantColors: dominantColors,
        mimeType: file.type,
        fileName: file.name,
        uploadedAt: new Date().toISOString(),
      },
      // Additional fields for compatibility
      original_url: originalUrlData.publicUrl,
      thumbnail_url: thumbnailUrl,
      low_res_url: thumbnailUrl, // Using thumbnail as low-res for now
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to fetch user's media assets
export async function GET(request: NextRequest) {
  try {
    // const { userId } = await auth()
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type') // 'image', 'video', or null for all
    const search = searchParams.get('search')
    const assetId = searchParams.get('id') // for fetching single asset

    const offset = (page - 1) * limit

    // Handle single asset fetch
    if (assetId) {
      const [asset] = await db
        .select()
        .from(mediaAssets)
        .where(and(eq(mediaAssets.id, assetId), eq(mediaAssets.userId, userId)))
        .limit(1)

      if (!asset) {
        return NextResponse.json({ assets: [], total_results: 0 })
      }

      const transformedAsset = {
        id: asset.id,
        width: asset.width,
        height: asset.height,
        duration: asset.duration ? parseFloat(asset.duration) : undefined,
        file_size: asset.fileSize,
        quality: asset.quality,
        tags: asset.metadata?.tags || [],
        url: asset.originalUrl,
        thumbnail: asset.thumbnailUrl,
        user: {
          id: asset.userId,
          name: 'User',
        },
        metadata: {
          aspectRatio: asset.metadata?.aspectRatio,
          orientation: asset.metadata?.orientation,
          dominantColors: asset.metadata?.dominantColors,
          mimeType: asset.mimeType,
          fileName: asset.originalName,
          uploadedAt: asset.createdAt?.toISOString(),
        },
        original_url: asset.originalUrl,
        thumbnail_url: asset.thumbnailUrl,
        low_res_url: asset.thumbnailUrl,
      }

      return NextResponse.json({
        assets: [transformedAsset],
        total_results: 1,
      })
    }

    // Build query conditions
    const whereConditions = [eq(mediaAssets.userId, userId)]

    if (type === 'image') {
      whereConditions.push(like(mediaAssets.mimeType, 'image/%'))
    } else if (type === 'video') {
      whereConditions.push(like(mediaAssets.mimeType, 'video/%'))
    } else if (!type || type === 'all') {
      // Only images and videos for 'all' or no type
      const imageCond = like(mediaAssets.mimeType, 'image/%')
      const videoCond = like(mediaAssets.mimeType, 'video/%')
      const orCondition = or(imageCond, videoCond)
      if (orCondition) {
        whereConditions.push(orCondition)
      }
    }

    if (search) {
      whereConditions.push(ilike(mediaAssets.originalName, `%${search}%`))
    }

    // Always have at least one condition
    let whereClause
    if (whereConditions.length === 0) {
      whereClause = eq(mediaAssets.userId, userId)
    } else if (whereConditions.length === 1) {
      whereClause = whereConditions[0]
    } else {
      whereClause = and(...whereConditions)
    }
    if (!whereClause) {
      whereClause = eq(mediaAssets.userId, userId)
    }

    // Run count and assets queries simultaneously for better performance
    const [totalCountResult, assets] = await Promise.all([
      db.select({ count: count() }).from(mediaAssets).where(whereClause),
      db
        .select()
        .from(mediaAssets)
        .where(whereClause)
        .orderBy(desc(mediaAssets.createdAt))
        .limit(limit)
        .offset(offset),
    ])

    const totalCount = totalCountResult[0].count

    // Transform to stock API format
    const transformedAssets = assets.map(asset => ({
      id: asset.id,
      width: asset.width,
      height: asset.height,
      duration: asset.duration ? parseFloat(asset.duration) : undefined,
      file_size: asset.fileSize,
      quality: asset.quality,
      tags: asset.metadata?.tags || [],
      url: asset.originalUrl,
      thumbnail: asset.thumbnailUrl,
      user: {
        id: asset.userId,
        name: 'User',
      },
      metadata: {
        aspectRatio: asset.metadata?.aspectRatio,
        orientation: asset.metadata?.orientation,
        dominantColors: asset.metadata?.dominantColors,
        mimeType: asset.mimeType,
        fileName: asset.originalName,
        uploadedAt: asset.createdAt?.toISOString(),
      },
      original_url: asset.originalUrl,
      thumbnail_url: asset.thumbnailUrl,
      low_res_url: asset.thumbnailUrl,
    }))

    return NextResponse.json({
      assets: transformedAssets,
      page,
      per_page: limit,
      total_results: totalCount,
    })
  } catch (error) {
    console.error('Fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
