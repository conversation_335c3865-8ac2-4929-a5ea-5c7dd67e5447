import { NextRequest, NextResponse } from 'next/server'
import {
  sendEmail,
  createVerificationEmailTemplate,
  createPasswordResetEmailTemplate,
  createWelcomeEmailTemplate,
} from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const { email, type = 'verification' } = await request.json()

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    // Test URL (in production, this would be the actual verification/reset URL)
    const testUrl = 'https://adori.ai/verify?token=test-token-123'

    let result
    if (type === 'verification') {
      const { html, text } = createVerificationEmailTemplate(
        testUrl,
        'Test User'
      )
      result = await sendEmail({
        to: email,
        subject: 'Test Email Verification - Adori AI',
        text,
        html,
      })
    } else if (type === 'reset') {
      const { html, text } = createPasswordResetEmailTemplate(
        testUrl,
        'Test User'
      )
      result = await sendEmail({
        to: email,
        subject: 'Test Password Reset - Adori AI',
        text,
        html,
      })
    } else if (type === 'welcome') {
      const { html, text } = createWelcomeEmailTemplate('Test User')
      result = await sendEmail({
        to: email,
        subject: 'Test Welcome Email - Adori AI',
        text,
        html,
      })
    } else {
      return NextResponse.json({ error: 'Invalid email type' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      messageId: result.MessageId,
      message: `${type} email sent successfully to ${email}`,
    })
  } catch (error) {
    console.error('Test email error:', error)
    return NextResponse.json(
      {
        error: 'Failed to send test email',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
