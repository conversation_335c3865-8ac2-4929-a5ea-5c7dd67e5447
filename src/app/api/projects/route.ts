import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { projects } from '@/db/schema'
import { eq, desc, count } from 'drizzle-orm'
import { onRequestError } from '../../../../instrumentation'
import { getUserSession } from '@/lib/user-utils'
import { getActiveOrganizationForUser } from '@/lib/organization-utils'

export async function GET(request: NextRequest) {
  try {
    const session = await getUserSession()

    // const { userId } = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const limit = parseInt(url.searchParams.get('limit') || '8', 10)
    const offset = (page - 1) * limit

    // Get the user's active organization
    const activeOrganization = await getActiveOrganizationForUser(userId)

    // Build the where condition based on whether user is in an organization
    let whereCondition
    if (activeOrganization) {
      // If user is in an organization, show projects from that organization
      whereCondition = eq(
        projects.organizationId,
        activeOrganization.organizationId
      )
    } else {
      // If user is not in an organization, show only their own projects
      whereCondition = eq(projects.userId, userId)
    }

    // Fetch projects with pagination
    const [projectsData, totalCountResult] = await Promise.all([
      db
        .select({
          projectId: projects.projectId,
          projectName: projects.projectName,
          method: projects.method,
          updatedAt: projects.updatedAt,
          createdAt: projects.createdAt,
          coverColor: projects.coverColor,
          coverPic: projects.coverPic,
          orientation: projects.orientation,
          duration: projects.duration,
        })
        .from(projects)
        .where(whereCondition)
        .orderBy(desc(projects.updatedAt))
        .limit(limit)
        .offset(offset),

      db.select({ count: count() }).from(projects).where(whereCondition),
    ])

    const totalCount = totalCountResult[0]?.count || 0
    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      projects: projectsData,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    await onRequestError(error, request)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getUserSession()

    // const { userId } = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session?.user?.id

    const { projectId } = await request.json()

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    }

    // Get the user's active organization
    const activeOrganization = await getActiveOrganizationForUser(userId)

    // Check if project exists and user has access to it
    const existingProject = await db
      .select()
      .from(projects)
      .where(eq(projects.projectId, projectId))
      .limit(1)

    if (!existingProject || existingProject.length === 0) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const project = existingProject[0]

    // Check if user has access to this project
    let hasAccess = false

    if (activeOrganization) {
      // If user is in an organization, they can delete projects from that organization
      hasAccess = project.organizationId === activeOrganization.organizationId
    } else {
      // If user is not in an organization, they can only delete their own projects
      hasAccess = project.userId === userId
    }

    if (!hasAccess) {
      return NextResponse.json(
        {
          error:
            'Access denied - You do not have permission to delete this project',
        },
        { status: 403 }
      )
    }

    // Delete the project
    await db.delete(projects).where(eq(projects.projectId, projectId))

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
