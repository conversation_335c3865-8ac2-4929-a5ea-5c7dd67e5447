import React from 'react'
import { Check, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Plan } from './types'

interface PricingTableProps {
  plans: Plan[]
  billingPeriod: 'monthly' | 'annual'
}

const FeatureItem = ({
  children,
  active = true,
}: {
  children: React.ReactNode
  active?: boolean
}) => (
  <div className='py-1.5 flex items-start gap-2.5 text-sm'>
    {active ? (
      <div className='h-4 w-4 mt-0.5 flex items-center justify-center shrink-0'>
        <Check className='h-3.5 w-3.5 text-green-500' />
      </div>
    ) : (
      <div className='h-4 w-4 mt-0.5 flex items-center justify-center shrink-0'>
        <X className='h-3.5 w-3.5 text-gray-400' />
      </div>
    )}
    <span>{children}</span>
  </div>
)

const PricingTable: React.FC<PricingTableProps> = ({
  plans,
  billingPeriod,
}) => {
  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-0'>
      {plans.map((plan, index) => {
        const isPremium = plan.name === 'Premium'
        const isFree = plan.name === 'Free'
        const displayPrice =
          billingPeriod === 'annual' && plan.annualPrice
            ? plan.annualPrice
            : plan.price

        return (
          <div
            key={index}
            className={cn(
              'flex flex-col border',
              // Min height to keep cards consistent
              'min-h-[600px]',
              // Premium styling
              isPremium
                ? 'border-primary bg-secondary/55 dark:text-white text-black relative z-10'
                : 'bg-background',
              // Mobile stacking corners
              'first:rounded-t-lg last:rounded-b-lg',
              // Tablet and desktop arrangement
              'sm:first:rounded-l-lg sm:first:rounded-tr-none',
              'sm:last:rounded-r-lg sm:last:rounded-bl-none',
              // Ensure premium has visible borders on all sides
              isPremium && 'lg:border-primary lg:shadow-xl lg:z-10',
              // On mobile, only first item has top border, rest have border-top-0
              index > 0 ? 'border-t-0 sm:border-t' : '',
              // On tablet/desktop, show left borders properly
              index > 0 ? 'sm:border-l-0' : '',
              // Specific border fix for premium plan
              isPremium
                ? 'sm:border-l sm:border-r lg:border-l lg:border-r'
                : '',
              // Scale premium slightly
              isPremium && 'lg:-mt-2 lg:-mb-2'
            )}
          >
            {/* Popular tag for premium */}
            {plan.highlightTag && (
              <div className='absolute top-0 inset-x-0 flex justify-center transform -translate-y-1/2'>
                <span className='text-xs font-medium bg-green-500 text-white px-3 py-1 rounded-full shadow-sm'>
                  {plan.highlightTag}
                </span>
              </div>
            )}

            {/* Plan name */}
            <div className='px-6 pt-6 pb-4 text-center'>
              <h3 className='text-lg font-medium'>{plan.name}</h3>
            </div>

            {/* Price */}
            <div className='px-6 pb-5 text-center'>
              <div className='flex items-end justify-center gap-1'>
                <span className='text-3xl font-bold'>{displayPrice}</span>
                <span className='text-sm mb-1 text-muted-foreground'>
                  {isFree ? plan.billingPeriod : 'per month'}
                </span>
              </div>

              {/* Annual Savings */}
              {billingPeriod === 'annual' && plan.annualSavings && (
                <div
                  className={cn(
                    'text-sm font-medium mt-1',
                    isPremium ? 'text-green-400' : 'text-green-500'
                  )}
                >
                  {plan.annualSavings}
                </div>
              )}

              <p className='text-sm mt-3 text-muted-foreground'>
                {plan.description}
              </p>
            </div>

            {/* CTA Button */}
            <div className='px-6 pb-6 text-center'>
              <Button
                className={cn(
                  'w-full py-5',
                  isPremium && 'bg-primary hover:bg-primary/90',
                  isPremium ? 'text-white' : ''
                )}
                variant={plan.buttonVariant}
              >
                {plan.buttonText}
              </Button>
            </div>

            {/* Features section */}
            <div
              className={cn(
                'px-6 py-5 border-t flex-grow',
                isPremium ? 'border-white/10' : 'border-border/60'
              )}
            >
              <h4 className='font-medium text-sm uppercase tracking-wide mb-4 text-muted-foreground'>
                What&apos;s included
              </h4>

              {/* Feature list */}
              <div className='space-y-2'>
                <FeatureItem>
                  {plan.videoCreation} video creation
                  {plan.videoCreation > 1 ? 's' : ''}
                </FeatureItem>

                <FeatureItem active={plan.videoDownloads > 0}>
                  {plan.videoDownloads > 0
                    ? `${plan.videoDownloads} video downloads`
                    : 'No video downloads'}
                </FeatureItem>

                <FeatureItem>
                  {plan.maxVideoLength} max video length
                </FeatureItem>

                <FeatureItem>{plan.resolution} resolution</FeatureItem>

                <FeatureItem>{plan.aiImageGenerator}</FeatureItem>

                <FeatureItem active={plan.publishToYoutube}>
                  {plan.publishToYoutube
                    ? 'YouTube publishing'
                    : 'No YouTube publishing'}
                </FeatureItem>

                <FeatureItem active={plan.proVoices}>
                  {plan.proVoices ? 'PRO voices' : 'Standard voices only'}
                </FeatureItem>

                <FeatureItem active={plan.voiceCloning}>
                  {plan.voiceCloning ? 'Voice cloning' : 'No voice cloning'}
                </FeatureItem>

                {plan.curationSupport && (
                  <FeatureItem>
                    {plan.curationSupport} curation support
                  </FeatureItem>
                )}
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export { PricingTable }
