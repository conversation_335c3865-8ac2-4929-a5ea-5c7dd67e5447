import React from 'react'
import { cn } from '@/lib/utils'

interface BillingToggleProps {
  activePeriod: 'monthly' | 'annual'
  onChange: (period: 'monthly' | 'annual') => void
}

const BillingToggle: React.FC<BillingToggleProps> = ({
  activePeriod,
  onChange,
}) => {
  return (
    <div className='inline-flex items-center bg-[#202030] border border-[#353545]/50 rounded-md p-1'>
      <button
        className={cn(
          'px-4 py-1.5 text-sm font-medium rounded-md transition-all',
          activePeriod === 'monthly'
            ? 'bg-primary text-white shadow-sm'
            : 'text-white/70 hover:text-white'
        )}
        onClick={() => onChange('monthly')}
      >
        Monthly
      </button>
      <button
        className={cn(
          'px-4 py-1.5 text-sm font-medium rounded-md transition-all',
          activePeriod === 'annual'
            ? 'bg-primary text-white shadow-sm'
            : 'text-white/70 hover:text-white'
        )}
        onClick={() => onChange('annual')}
      >
        Annual
      </button>
    </div>
  )
}

export default BillingToggle
