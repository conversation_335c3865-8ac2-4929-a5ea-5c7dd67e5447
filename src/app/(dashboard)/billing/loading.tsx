import { ClerkPricingSkeleton } from '@/components/ui/clerk-pricing-skeleton'

export default function PricingLoading() {
  return (
    <div className='max-w-4xl mx-auto py-12 px-4 sm:px-6'>
      {/* Optimized page header skeleton */}
      <header
        className='mb-12 text-center space-y-4'
        aria-label='Loading pricing page'
      >
        <div className='h-9 w-64 bg-muted rounded-md mx-auto animate-pulse' />
        <div className='h-6 w-96 bg-muted rounded-md mx-auto animate-pulse' />
      </header>

      {/* Enhanced Clerk Pricing Table Skeleton */}
      <section className='mb-16' aria-label='Loading pricing plans'>
        <ClerkPricingSkeleton />
      </section>

      {/* Optimized enterprise section skeleton */}
      <section
        className='w-full mx-auto rounded-lg bg-muted/20 border p-8 text-center animate-pulse'
        aria-label='Loading enterprise contact section'
      >
        <div className='max-w-lg mx-auto space-y-4'>
          <div className='h-6 w-48 bg-muted rounded-md mx-auto' />
          <div className='space-y-2'>
            <div className='h-4 w-full bg-muted rounded-md' />
            <div className='h-4 w-3/4 bg-muted rounded-md mx-auto' />
          </div>
          <div className='h-5 w-40 bg-muted rounded-md mx-auto' />
        </div>
      </section>
    </div>
  )
}
