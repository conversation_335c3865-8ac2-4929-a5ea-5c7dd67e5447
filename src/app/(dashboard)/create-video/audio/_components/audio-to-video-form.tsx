'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import {
  AudioLines,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  BarChart3,
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { LoaderDialog } from '@/components/ui/loader-dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/lib/toast'
import { uploadFile } from '@/lib/upload'
import type { AutomationRequest } from '@/lib/types'
import { useInngestRunStatus } from '@/lib/useInngestRunStatus'
import { useQueryClient } from '@tanstack/react-query'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { useVideoStore } from '@/store/video-store'

// Import reusable selector components
import {
  OrientationSelector,
  AutopickSelector,
  ClipPaceSelector,
  AudioFormSkeleton,
  ActionButton,
} from '@/app/(dashboard)/_components/video-form'

// Simplified interface for file state
interface FileWithPreview {
  id: string
  name: string
  size: number
  type: string
  lastModified: number
  progress: number
  status: 'uploading' | 'completed' | 'error'
  file: File
  url: string
  duration?: string
  durationSec?: number
}

interface VideoConfig {
  orientation: 'landscape' | 'portrait' | 'square'
  autopick: string
}

const initialConfig: VideoConfig = {
  orientation: 'landscape',
  autopick: 'mix',
}

// File Upload Dropzone Component
const FileUploadDropzone = ({
  files,
  onFileSelect,
  onRemoveFile,
  isDragActive,
  onDragOver,
  onDragLeave,
  onDrop,
}: {
  files: FileWithPreview[]
  onFileSelect: (files: FileList) => void
  onRemoveFile: (id: string) => void
  isDragActive: boolean
  onDragOver: (e: React.DragEvent) => void
  onDragLeave: () => void
  onDrop: (e: React.DragEvent) => void
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      onFileSelect(e.target.files)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (!bytes) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
  }

  return (
    <div className='w-full space-y-4'>
      {/* Upload area when no files */}
      {files.length === 0 && (
        <div
          onClick={handleClick}
          onDragOver={onDragOver}
          onDragLeave={onDragLeave}
          onDrop={onDrop}
          className={`p-10 rounded-lg cursor-pointer w-full border-2 border-dashed transition-colors ${
            isDragActive
              ? 'border-primary bg-primary/5'
              : 'border-border hover:border-primary'
          }`}
        >
          <input
            ref={fileInputRef}
            type='file'
            accept='audio/*,.mp3,.wav,.flac,.aac,.ogg,.m4a'
            onChange={handleFileChange}
            className='hidden'
          />
          <div className='flex flex-col items-center justify-center space-y-4'>
            <AudioLines
              className={`w-16 h-16 transition-colors ${
                isDragActive ? 'text-primary' : 'text-muted-foreground'
              }`}
            />
            <p className='font-bold text-foreground text-base'>
              {isDragActive ? 'Drop audio file here' : 'Upload audio file'}
            </p>
            <p className='text-muted-foreground text-base'>
              {isDragActive ? (
                <span className='font-medium text-primary'>
                  Release to upload
                </span>
              ) : (
                <>
                  Drag & drop an audio file here, or{' '}
                  <span className='text-primary font-medium'>browse</span>
                </>
              )}
            </p>
            <div className='flex gap-2'>
              <Badge variant='secondary' className='text-xs'>
                MP3
              </Badge>
              <Badge variant='secondary' className='text-xs'>
                WAV
              </Badge>
              <Badge variant='secondary' className='text-xs'>
                FLAC
              </Badge>
              <Badge variant='secondary' className='text-xs'>
                AAC
              </Badge>
            </div>
            <p className='text-sm text-muted-foreground'>
              Supports audio files up to 50MB
            </p>
          </div>
        </div>
      )}

      {/* File list when files uploaded */}
      {files.length > 0 && (
        <div className='space-y-3'>
          <h3 className='font-semibold text-lg text-foreground'>
            Uploaded File
          </h3>
          {files.map(file => (
            <div
              key={file.id}
              className='flex items-center gap-4 p-4 bg-card rounded-xl border border-border'
            >
              <div className='flex-shrink-0'>
                <BarChart3 className='w-10 h-10 text-orange-500' />
              </div>
              <div className='flex-1 min-w-0'>
                <div className='flex items-center justify-between'>
                  <h4
                    className='font-medium text-foreground truncate'
                    title={file.name}
                  >
                    {file.name}
                  </h4>
                  <div className='flex items-center gap-2'>
                    {file.status === 'uploading' && (
                      <Loader2 className='w-4 h-4 animate-spin text-primary' />
                    )}
                    {file.status === 'completed' && (
                      <CheckCircle className='w-4 h-4 text-green-500' />
                    )}
                    {file.status === 'error' && (
                      <AlertCircle className='w-4 h-4 text-red-500' />
                    )}
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => onRemoveFile(file.id)}
                      className='h-8 w-8 p-0 text-muted-foreground hover:text-red-500'
                    >
                      <X className='w-4 h-4' />
                    </Button>
                  </div>
                </div>
                <div className='flex items-center justify-between mt-1'>
                  <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                    <span>{formatFileSize(file.size)}</span>
                    {file.duration && (
                      <>
                        <span>•</span>
                        <span>{file.duration}</span>
                      </>
                    )}
                  </div>
                  {file.status === 'uploading' && (
                    <div className='flex items-center gap-2'>
                      <Progress value={file.progress} className='w-20 h-2' />
                      <span className='text-xs text-muted-foreground'>
                        {Math.round(file.progress)}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export function AudioToVideoForm() {
  const router = useRouter()
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const queryClient = useQueryClient()

  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [config, setConfig] = useState<VideoConfig>(initialConfig)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isDragActive, setIsDragActive] = useState(false)
  const [clipPace, setClipPace] = useState<
    'fast' | 'medium' | 'slow' | 'verySlow'
  >('medium')
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  // Loading state tracking
  const [progressMessage, setProgressMessage] = useState('')
  const [eventId, setEventId] = useState<string | null>(null)

  const { status, output, error } = useInngestRunStatus(eventId)

  // Handle initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  // Handle successful video generation
  useEffect(() => {
    if (
      status === 'Completed' &&
      output &&
      typeof output === 'object' &&
      output !== null &&
      'projectId' in output &&
      typeof output.projectId === 'string'
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const project = output as any
      setProgressMessage('🎉 Video generation complete! Redirecting...')
      useVideoStore.getState().setProjectData(project)
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Video created successfully!')
      setTimeout(() => {
        router.push(`/scene-editor?projectId=${project.projectId}`)
      }, 1500)
      setIsGenerating(false)
    } else if (status === 'Failed' || status === 'Cancelled' || error) {
      setProgressMessage('')
      let errorMessage = 'Video generation failed. Please try again.'

      if (error) {
        errorMessage = `Error: ${error}`
      } else if (output && typeof output === 'object' && 'error' in output) {
        errorMessage = `Error: ${output.error}`
      } else if (output && typeof output === 'string') {
        errorMessage = `Error: ${output}`
      }

      toast.error(errorMessage)
      setIsGenerating(false)
    }
  }, [status, output, error, queryClient, router])

  // Update config helper
  const updateConfig = (key: keyof VideoConfig, value: string) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  // Progress simulation
  const simulateProgress = () => {
    setProgressMessage(
      '🎵 Processing audio content and extracting transcript...'
    )
    setTimeout(
      () =>
        setProgressMessage('🤖 Converting audio transcript to video script...'),
      2000
    )
    setTimeout(
      () =>
        setProgressMessage(
          '🎬 Finding perfect visual assets for your content...'
        ),
      4000
    )
    setTimeout(
      () =>
        setProgressMessage(
          '🎙️ Converting script to professional voiceovers...'
        ),
      6000
    )
    setTimeout(
      () =>
        setProgressMessage('✨ Finalizing scenes and preparing your video...'),
      8000
    )
    setTimeout(
      () => setProgressMessage('🎉 Almost done! Wrapping everything up...'),
      10000
    )
  }

  // File upload handlers
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      if (files.length === 0) {
        setIsDragActive(true)
      }
    },
    [files.length]
  )

  const handleDragLeave = useCallback(() => {
    setIsDragActive(false)
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragActive(false)

      if (files.length > 0) {
        toast.error(
          'Please remove the current file before uploading a new one.'
        )
        return
      }

      const droppedFiles = Array.from(e.dataTransfer.files)
      const audioFiles = droppedFiles.filter(
        file =>
          file.type.startsWith('audio/') ||
          ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'].some(ext =>
            file.name.toLowerCase().endsWith(ext)
          )
      )

      if (audioFiles.length === 0) {
        toast.error('Please upload only audio files.')
        return
      }

      if (audioFiles.length > 1) {
        toast.error('Please upload only one audio file at a time.')
        return
      }

      const fileList = new DataTransfer()
      fileList.items.add(audioFiles[0])
      handleFileSelect(fileList.files)
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [files.length]
  )

  const handleFileSelect = async (fileList: FileList) => {
    if (files.length > 0) {
      toast.error('Please remove the current file before uploading a new one.')
      return
    }

    if (fileList.length === 0) return

    const file = fileList[0]
    const newFile: FileWithPreview = {
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      progress: 0,
      status: 'uploading' as const,
      file,
      url: '',
      duration: undefined,
      durationSec: undefined,
    }

    setFiles([newFile])

    try {
      const result = await uploadFile(file)

      setFiles([
        {
          ...newFile,
          progress: 100,
          status: 'completed',
          url: result.url,
        },
      ])

      toast.success('Audio file uploaded successfully!')

      // Get duration asynchronously
      const audioUrl = URL.createObjectURL(file)
      const audio = new window.Audio(audioUrl)
      audio.addEventListener('loadedmetadata', () => {
        const durationSec = audio.duration
        const minutes = Math.floor(durationSec / 60)
        const seconds = Math.floor(durationSec % 60)
        const formatted = `${minutes}:${seconds.toString().padStart(2, '0')}`
        setFiles(prev =>
          prev.map(f =>
            f.id === newFile.id ? { ...f, duration: formatted, durationSec } : f
          )
        )
        URL.revokeObjectURL(audioUrl)
      })
      audio.load()
    } catch (error) {
      setFiles([
        {
          ...newFile,
          progress: 100,
          status: 'error',
        },
      ])
      toast.error(
        `Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  const handleRemoveFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  // Handle video generation
  const handleGenerateVideo = async () => {
    if (files.length === 0) {
      toast.error('Please upload an audio file')
      return
    }

    const userId = session?.user?.id
    if (!userId) {
      toast.error('You must be logged in to generate videos')
      return
    }

    setIsGenerating(true)
    simulateProgress()

    try {
      const selectedFile = files[0]
      const automationRequest: AutomationRequest = {
        idea: '',
        audioUrl: selectedFile.url,
        audioName: selectedFile.name,
        tone: 'friendly',
        audience: 'general',
        platform: 'youtube',
        hook: true,
        callToAction: true,
        keywords: undefined,
        duration: selectedFile.durationSec || 60,
        language: 'english',
        orientation: config.orientation,
        autopick: config.autopick,
        clipPace,
        voice: null,
        userId: userId,
        organizationId: session?.session?.activeOrganizationId || undefined,
        method: 'Audio to Video',
      }

      const response = await fetch('/api/generate-video-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(automationRequest),
      })

      if (!response.ok) throw new Error('Failed to generate video')

      const result = await response.json()
      if (result.eventId) {
        setEventId(result.eventId)
      }
    } catch (error) {
      console.error('Video generation error:', error)
      setProgressMessage('')
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to generate video. Please try again.'
      )
      setIsGenerating(false)
    }
  }

  const canGenerate = files.length > 0 && !isGenerating

  // Show skeleton while initially loading
  if (isInitialLoading) {
    return <AudioFormSkeleton />
  }

  return (
    <>
      {/* Audio Upload Section */}
      <div className='mb-4'>
        <FileUploadDropzone
          files={files}
          onFileSelect={handleFileSelect}
          onRemoveFile={handleRemoveFile}
          isDragActive={isDragActive}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        />
      </div>

      {/* Configuration Section */}
      <Card>
        <CardContent>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex space-x-4 space-y-2 flex-wrap'>
                <OrientationSelector
                  value={config.orientation}
                  onValueChange={value => updateConfig('orientation', value)}
                  disabled={isGenerating}
                  compact={true}
                />

                <AutopickSelector
                  value={config.autopick}
                  onValueChange={value => updateConfig('autopick', value)}
                  disabled={isGenerating}
                  compact={true}
                />

                <ClipPaceSelector
                  value={clipPace}
                  onValueChange={setClipPace}
                  disabled={isGenerating}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Button */}
      <ActionButton
        onClick={handleGenerateVideo}
        disabled={!canGenerate}
        isGenerating={isGenerating}
        progressMessage={progressMessage}
        actionText='Generate Video'
        loadingText='Creating Video...'
      />

      {/* Loading Dialog */}
      <LoaderDialog
        open={isGenerating}
        title='Generating Your Audio Video'
        subtitle={progressMessage}
      />
    </>
  )
}
