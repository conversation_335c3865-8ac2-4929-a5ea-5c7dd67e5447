'use client'

import { useState, FormEvent, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Label } from '@/components/ui/label'
import { LoaderDialog } from '@/components/ui/loader-dialog'
import { toast } from '@/lib/toast'
import { useVideoStore } from '@/store/video-store'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { useInngestRunStatus } from '@/lib/useInngestRunStatus'
import { useQueryClient } from '@tanstack/react-query'
import { TextEditor } from '@/components/blocks/text-editor'

// Import reusable components
import {
  ToneSelector,
  AudienceSelector,
  PlatformSelector,
  IncludeOptions,
  KeywordsInput,
  AdvancedSettings,
  ActionButton,
  SideBySideSkeleton,
  type TextVideoConfig,
} from '@/app/(dashboard)/_components/video-form'

// Use the reusable configuration type
type VideoConfig = TextVideoConfig

export function TextToVideoForm() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  // Loading state tracking
  const [progressMessage, setProgressMessage] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)

  const [config, setConfig] = useState<VideoConfig>({
    text: '',
    tone: 'friendly',
    audience: 'general',
    platform: 'youtube',
    duration: 60,
    includeHook: true,
    includeCTA: true,
    language: 'english',
    orientation: 'landscape',
    autopick: 'mix',
    voice: null,
    creativity: 50,
    keywords: '',
  })

  const [eventId, setEventId] = useState<string | null>(null)
  const { status, output } = useInngestRunStatus(eventId)

  const updateConfig = (
    key: keyof VideoConfig,
    value: VideoConfig[keyof VideoConfig]
  ) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  const handleVoiceSelect = (voice: ElevenVoice) => {
    updateConfig('voice', voice)
    toast.success(`Voice "${voice.name}" selected`)
  }

  const simulateProgress = () => {
    setProgressMessage(
      '📄 Processing your text content and analyzing structure...'
    )

    setTimeout(() => {
      setProgressMessage('🎬 Finding perfect visual assets and media...')
    }, 2000)

    setTimeout(() => {
      setProgressMessage('🎙️ Converting text to professional voiceovers...')
    }, 4000)

    setTimeout(() => {
      setProgressMessage('✨ Finalizing scenes and preparing your video...')
    }, 6000)

    setTimeout(() => {
      setProgressMessage('🎉 Almost done! Wrapping everything up...')
    }, 8000)
  }

  const handleGenerateScript = async (e?: FormEvent) => {
    e?.preventDefault()

    if (!config.text.trim()) {
      toast.error('Please enter your text content')
      return
    }

    if (!config.tone || !config.audience || !config.platform) {
      toast.error('Please select tone, audience, and platform')
      return
    }

    if (!config.voice) {
      toast.error('Please select a voice for the video')
      return
    }

    setIsGenerating(true)
    simulateProgress()

    const userId = session?.user?.id

    try {
      // Prepare automation request
      const automationRequest = {
        idea: config.text,
        tone: config.tone,
        audience: config.audience,
        platform: config.platform,
        hook: config.includeHook,
        callToAction: config.includeCTA,
        keywords: config.keywords || undefined,
        duration: config.duration,
        language: config.language,
        orientation: config.orientation,
        autopick: config.autopick,
        voice: config.voice,
        userId: userId,
        organizationId: session?.session?.activeOrganizationId || undefined,
        method: 'Text to Video',
      }

      // Call the inngest API route
      const response = await fetch('/api/generate-video-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(automationRequest),
      })
      if (!response.ok) throw new Error('Failed to generate video')
      const result = await response.json()
      setEventId(result.eventId)
    } catch (error) {
      console.error('Video generation error:', error)
      setProgressMessage('')
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to generate video. Please try again.'
      )
      setIsGenerating(false)
    }
  }

  // Watch for polling completion
  useEffect(() => {
    if (
      status === 'Completed' &&
      output &&
      typeof output === 'object' &&
      output !== null &&
      'projectId' in output
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const project = output as any // API response will be transformed by setProjectData
      setProgressMessage('🎉 Video generation complete! Redirecting...')
      useVideoStore.getState().setProjectData(project)
      // Invalidate projects cache when a new project is created via Inngest
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Video created successfully!')
      setTimeout(() => {
        router.push(`/scene-editor?projectId=${project.projectId}`)
      }, 1500)
      setIsGenerating(false)
    } else if (status === 'Failed' || status === 'Cancelled') {
      setProgressMessage('')
      toast.error('Video generation failed. Please try again.')
      setIsGenerating(false)
    }
  }, [status, output, queryClient, router])

  // Simulate initial loading for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 100) // Just enough time to prevent layout shift

    return () => clearTimeout(timer)
  }, [])

  // Show skeleton during initial loading
  if (isInitialLoading) {
    return <SideBySideSkeleton leftSideType='text' />
  }

  const wordCount = config.text
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0).length
  const charCount = config.text.length

  return (
    <>
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Left Column - Text Editor */}
        <div className='space-y-3'>
          <Label htmlFor='text-editor' className='text-sm font-medium'>
            Enter your text content
          </Label>

          {/* Rich Text Editor */}
          <TextEditor
            value={config.text}
            onChange={text => updateConfig('text', text)}
            disabled={isGenerating}
            className='min-h-[440px]'
          />

          {/* Word and Character Count */}
          <div className='flex justify-between text-xs text-muted-foreground'>
            <span>Words: {wordCount}</span>
            <span>Characters: {charCount}</span>
          </div>
        </div>

        {/* Right Column - Video Configuration */}
        <div className='space-y-4'>
          <Label className='text-sm font-medium'>Video Configuration</Label>

          {/* Tone, Audience, Platform Row */}
          <div className='flex gap-x-4 gap-y-2 flex-wrap'>
            <ToneSelector
              value={config.tone}
              onValueChange={value => updateConfig('tone', value)}
              disabled={isGenerating}
            />
            <AudienceSelector
              value={config.audience}
              onValueChange={value => updateConfig('audience', value)}
              disabled={isGenerating}
            />
            <PlatformSelector
              value={config.platform}
              onValueChange={value => updateConfig('platform', value)}
              disabled={isGenerating}
            />
          </div>

          {/* Include Options */}
          <IncludeOptions
            includeHook={config.includeHook}
            includeCTA={config.includeCTA}
            onIncludeHookChange={checked =>
              updateConfig('includeHook', checked)
            }
            onIncludeCTAChange={checked => updateConfig('includeCTA', checked)}
            disabled={isGenerating}
          />

          {/* Keywords */}
          <KeywordsInput
            value={config.keywords}
            onChange={value => updateConfig('keywords', value)}
            placeholder='e.g. technology, innovation, future'
            disabled={isGenerating}
          />

          {/* Advanced Settings */}
          <AdvancedSettings
            duration={config.duration}
            onDurationChange={value => updateConfig('duration', value)}
            voice={config.voice}
            onVoiceSelect={handleVoiceSelect}
            language={config.language}
            onLanguageChange={value => updateConfig('language', value)}
            orientation={config.orientation}
            onOrientationChange={value => updateConfig('orientation', value)}
            autopick={config.autopick}
            onAutopickChange={value => updateConfig('autopick', value)}
            disabled={isGenerating}
          />
        </div>
      </div>

      {/* Action Button */}
      <ActionButton
        onClick={handleGenerateScript}
        disabled={!config.text.trim() || !config.voice}
        isGenerating={isGenerating}
        progressMessage={progressMessage}
        actionText='Generate Video'
        loadingText='Creating Video...'
      />

      {/* Loader Dialog */}
      <LoaderDialog
        open={isGenerating || status === 'Running'}
        title='AI agent is cooking your video'
        subtitle={
          status === 'Running'
            ? 'Processing your request...'
            : progressMessage || 'This may take a few minutes...'
        }
      />
    </>
  )
}
