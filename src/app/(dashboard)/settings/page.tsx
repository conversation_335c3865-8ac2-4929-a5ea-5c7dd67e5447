'use client'

import { useState } from 'react'
import { Settings, Youtube, Key, Building2 } from 'lucide-react'
import { ThemeToggle } from '@/components/theme-toggle'
import { AccountSettings } from './_components/account-settings'
import { ApiKeys } from './_components/api-keys'
import { YouTubeConnection } from './_components/youtube-connection'
import { SimpleOrganizationWorkspace } from './_components/simple-organization-workspace'

// Custom tab button component matching media picker modal style
const TabButton = ({
  active,
  onClick,
  icon,
  label,
}: {
  active: boolean
  onClick: () => void
  icon: React.ReactNode
  label: string
}) => (
  <button
    className={`py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-colors ${
      active
        ? 'text-primary border-b-2 border-primary'
        : 'text-muted-foreground hover:text-foreground hover:bg-muted/40'
    }`}
    onClick={onClick}
  >
    {icon}
    <span className='hidden sm:inline'>{label}</span>
    <span className='sm:hidden'>{label.split(' ')[0]}</span>
  </button>
)

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general')

  return (
    <div className='container mx-auto p-6'>
      <div className='space-y-6'>
        {/* Page Header */}
        <div>
          <h1 className='text-2xl font-bold'>Settings</h1>
          <p className='text-muted-foreground'>
            Manage your account settings and preferences
          </p>
        </div>

        {/* Tabbed Interface */}
        <div className='bg-card rounded-lg overflow-hidden'>
          {/* Tab Navigation */}
          <div className='border-b border-border flex pl-4'>
            <div className='flex'>
              <TabButton
                active={activeTab === 'general'}
                onClick={() => setActiveTab('general')}
                icon={<Settings className='h-4 w-4' />}
                label='General'
              />
              <TabButton
                active={activeTab === 'youtube'}
                onClick={() => setActiveTab('youtube')}
                icon={<Youtube className='h-4 w-4' />}
                label='YouTube'
              />
              <TabButton
                active={activeTab === 'api-keys'}
                onClick={() => setActiveTab('api-keys')}
                icon={<Key className='h-4 w-4' />}
                label='API Keys'
              />
              <TabButton
                active={activeTab === 'organization'}
                onClick={() => setActiveTab('organization')}
                icon={<Building2 className='h-4 w-4' />}
                label='Workspace'
              />
            </div>
          </div>

          {/* Tab Content */}
          <div className='p-6'>
            {/* General Tab */}
            {activeTab === 'general' && (
              <div className='space-y-6'>
                {/* Theme Settings */}
                <div>
                  <h2 className='text-xl font-semibold mb-4'>Appearance</h2>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='font-medium'>Theme</p>
                      <p className='text-sm text-muted-foreground'>
                        Customize the appearance of the application
                      </p>
                    </div>
                    <ThemeToggle />
                  </div>
                </div>

                {/* Account Settings */}
                <div>
                  <h2 className='text-xl font-semibold mb-4'>Account</h2>
                  <p className='text-sm text-muted-foreground mb-4'>
                    Manage your account settings and preferences
                  </p>
                  <AccountSettings />
                </div>
              </div>
            )}

            {/* YouTube Tab */}
            {activeTab === 'youtube' && <YouTubeConnection />}

            {/* API Keys Tab */}
            {activeTab === 'api-keys' && <ApiKeys />}

            {/* Organization Workspace Tab */}
            {activeTab === 'organization' && <SimpleOrganizationWorkspace />}
          </div>
        </div>
      </div>
    </div>
  )
}
