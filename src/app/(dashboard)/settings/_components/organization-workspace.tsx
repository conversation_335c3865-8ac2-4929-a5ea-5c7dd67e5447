'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, MoreH<PERSON>zon<PERSON>, Check, X, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Separator } from '@/components/ui/separator'
import { useOrganization } from '@/hooks/use-organization'
import { InviteMemberModal } from './invite-member-modal'
import { toast } from 'sonner'
import { z } from 'zod'

// Validation schema for organization name
const organizationNameSchema = z
  .string()
  .min(1, 'Organization name is required')
  .max(30, 'Organization name cannot exceed 30 characters')
  .trim()

export function OrganizationWorkspace() {
  const [activeTeamTab, setActiveTeamTab] = useState<'members' | 'pending'>(
    'members'
  )
  const [isEditing, setIsEditing] = useState(false)
  const [tempOrgName, setTempOrgName] = useState('')
  const [originalOrgName, setOriginalOrgName] = useState('')
  const [nameError, setNameError] = useState<string | null>(null)

  const {
    activeOrganization,
    members,
    invitations,
    loading,
    updating,
    updateOrganization,
    inviteMember,
    removeMember,
    updateMemberRole,
    cancelInvitation,
    resendInvitation,
  } = useOrganization()

  // Validation function
  const validateOrgName = (name: string) => {
    try {
      organizationNameSchema.parse(name)
      setNameError(null)
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        setNameError(error.errors[0].message)
      }
      return false
    }
  }

  // Handle organization name change with validation
  const handleOrgNameChange = (value: string) => {
    setTempOrgName(value)
    if (value.trim()) {
      validateOrgName(value)
    } else {
      setNameError(null)
    }
  }

  // Initialize temp name when organization loads and set active organization if needed
  useEffect(() => {
    if (activeOrganization) {
      setTempOrgName(activeOrganization.name)
      setOriginalOrgName(activeOrganization.name)
    } else if (!loading && !activeOrganization) {
      // Try to set active organization if user has one but it's not set
      setActiveOrganizationIfNeeded()
    }
  }, [activeOrganization, loading])

  const setActiveOrganizationIfNeeded = async () => {
    try {
      const response = await fetch('/api/organization/active', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.organization) {
          // Refresh the page to show the organization
          window.location.reload()
        }
      }
    } catch (error) {
      console.error('Failed to set active organization:', error)
    }
  }

  const handleCopySlug = async () => {
    if (!activeOrganization) return

    try {
      await navigator.clipboard.writeText(activeOrganization.slug)
      toast.success('Organization slug copied to clipboard')
    } catch {
      toast.error('Failed to copy slug')
    }
  }

  const handleSaveWorkspace = async () => {
    if (!activeOrganization) return

    // Validate the organization name before saving
    if (!validateOrgName(tempOrgName)) {
      toast.error('Please fix the validation errors before saving')
      return
    }

    try {
      await updateOrganization({ name: tempOrgName })
      setIsEditing(false)
      setOriginalOrgName(tempOrgName)
      setNameError(null) // Clear any validation errors
    } catch {
      // Error handling is done in the hook
    }
  }

  const handleCancelEdit = () => {
    setTempOrgName(originalOrgName)
    setIsEditing(false)
    setNameError(null) // Clear validation errors
  }

  const handleMemberAction = async (action: string, memberId: string) => {
    if (!activeOrganization) return

    try {
      switch (action) {
        case 'remove':
          await removeMember(memberId)
          break
        case 'updateRole':
          // For now, we'll just toggle between member and admin
          const member = members.find(m => m.id === memberId)
          if (member) {
            const newRole = member.role === 'member' ? 'admin' : 'member'
            await updateMemberRole(memberId, newRole)
          }
          break
        default:
          break
      }
    } catch {
      // Error handling is done in the hook
    }
  }

  const handleInvitationAction = async (
    action: string,
    invitationId: string
  ) => {
    if (!activeOrganization) return

    try {
      switch (action) {
        case 'cancel':
          await cancelInvitation(invitationId)
          break
        case 'resend':
          await resendInvitation(invitationId)
          break
        default:
          break
      }
    } catch {
      // Error handling is done in the hook
    }
  }

  const getRoleBadge = (role: string) => {
    return role === 'owner' ? (
      <Badge variant='default' className='bg-primary text-primary-foreground'>
        Owner
      </Badge>
    ) : role === 'admin' ? (
      <Badge
        variant='secondary'
        className='bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      >
        Admin
      </Badge>
    ) : (
      <Badge variant='secondary'>Member</Badge>
    )
  }

  if (loading) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <Loader2 className='h-8 w-8 animate-spin' />
            <span className='ml-2'>Loading organization data...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!activeOrganization) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='text-center'>
              <h3 className='text-lg font-semibold mb-2'>
                No Active Organization
              </h3>
              <p className='text-muted-foreground'>
                You need to be part of an organization to view workspace
                settings.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
      {/* Workspace Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Workspace Settings</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Organization Name */}
          <div className='flex items-center justify-between'>
            <div className='flex-1'>
              <label className='text-sm font-medium'>Organization name</label>
              {isEditing ? (
                <div className='space-y-2'>
                  <div className='flex items-center gap-2'>
                    <Input
                      value={tempOrgName}
                      onChange={e => handleOrgNameChange(e.target.value)}
                      className='flex-1'
                      disabled={updating}
                    />
                    <Button
                      size='sm'
                      onClick={handleSaveWorkspace}
                      className='bg-primary text-primary-foreground'
                      disabled={
                        updating ||
                        tempOrgName === originalOrgName ||
                        nameError !== null
                      }
                    >
                      {updating ? (
                        <Loader2 className='h-4 w-4 animate-spin' />
                      ) : (
                        <Check className='h-4 w-4' />
                      )}
                    </Button>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={handleCancelEdit}
                      disabled={updating}
                    >
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                  {nameError && (
                    <p className='text-sm text-destructive'>{nameError}</p>
                  )}
                </div>
              ) : (
                <div className='flex items-center justify-between mt-1'>
                  <p className='text-sm text-muted-foreground'>
                    {activeOrganization.name}
                  </p>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={() => setIsEditing(true)}
                    disabled={updating}
                  >
                    Edit
                  </Button>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Organization Slug */}
          <div className='flex items-center justify-between'>
            <div className='flex-1'>
              <label className='text-sm font-medium'>Organization slug</label>
              <div className='flex items-center gap-2 mt-1'>
                <Input
                  value={activeOrganization.slug}
                  readOnly
                  className='flex-1 bg-muted'
                />
                <Button
                  size='sm'
                  variant='outline'
                  onClick={handleCopySlug}
                  disabled={updating}
                >
                  <Copy className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Members */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>Team Members</CardTitle>
            <InviteMemberModal onInvite={inviteMember} loading={updating} />
          </div>
        </CardHeader>
        <CardContent>
          {/* Team Member Tabs */}
          <div className='border-b border-border mb-4'>
            <div className='flex'>
              <button
                className={`py-2 px-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTeamTab === 'members'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
                onClick={() => setActiveTeamTab('members')}
              >
                Members ({members.length})
              </button>
              <button
                className={`py-2 px-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTeamTab === 'pending'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
                onClick={() => setActiveTeamTab('pending')}
              >
                Pending ({invitations.length})
              </button>
            </div>
          </div>

          {/* Members Table */}
          {activeTeamTab === 'members' && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className='w-[100px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={3}
                      className='text-center text-muted-foreground py-8'
                    >
                      No members found
                    </TableCell>
                  </TableRow>
                ) : (
                  members.map(member => (
                    <TableRow key={member.id}>
                      <TableCell className='font-medium'>
                        {member.user?.email || 'Unknown email'}
                      </TableCell>
                      <TableCell>{getRoleBadge(member.role)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant='ghost'
                              size='sm'
                              disabled={updating}
                            >
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem
                              onClick={() =>
                                handleMemberAction('updateRole', member.id)
                              }
                              disabled={updating}
                            >
                              {member.role === 'member'
                                ? 'Make Admin'
                                : 'Make Member'}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                handleMemberAction('remove', member.id)
                              }
                              disabled={updating || member.role === 'owner'}
                              className={
                                member.role === 'owner'
                                  ? 'text-muted-foreground'
                                  : ''
                              }
                            >
                              Remove member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}

          {/* Pending Members Table */}
          {activeTeamTab === 'pending' && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className='w-[100px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invitations.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={3}
                      className='text-center text-muted-foreground py-8'
                    >
                      No pending invitations
                    </TableCell>
                  </TableRow>
                ) : (
                  invitations.map(invitation => (
                    <TableRow key={invitation.id}>
                      <TableCell className='font-medium'>
                        {invitation.email}
                      </TableCell>
                      <TableCell>{getRoleBadge(invitation.role)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant='ghost'
                              size='sm'
                              disabled={updating}
                            >
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem
                              onClick={() =>
                                handleInvitationAction('resend', invitation.id)
                              }
                              disabled={updating}
                            >
                              Resend invitation
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                handleInvitationAction('cancel', invitation.id)
                              }
                              disabled={updating}
                            >
                              Cancel invitation
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
