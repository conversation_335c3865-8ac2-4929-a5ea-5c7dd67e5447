/**
 * Remotion Shared Library
 *
 * This library provides shared components, hooks, and utilities for both
 * video preview and rendering systems, ensuring consistency and reducing
 * code duplication while maintaining full backward compatibility.
 */

// Export specific items instead of barrel exports to reduce memory usage during build
export { UnifiedComposition } from './compositions/UnifiedComposition'
export { ErrorBoundary } from './components/ErrorBoundary'
export { SubtitleDisplay } from './components/subtitles/SubtitleDisplay'
export { SceneVisual } from './components/core/SceneVisual'
export { ScenesVideo } from './components/core/ScenesVideo'

// Hooks
export { useSubtitleSystem } from './hooks/useSubtitleSystem'
export { useAudioPreloader } from './hooks/useAudioPreloader'
export { useImagePreloader } from './hooks/useImagePreloader'

// Utils - export specific functions to reduce bundle size
export {
  generateCaptionsFromElevenLabs,
  generateBasicCaptions,
} from './utils/captionUtils'
export { preloadAudio, getAudioDuration } from './utils/audioUtils'
export { fadeIn, fadeOut, slideIn, slideOut } from './utils/transitionUtils'

// Types
export type * from './types/index'

// Dynamic imports for better bundle splitting
export * from './dynamic'
