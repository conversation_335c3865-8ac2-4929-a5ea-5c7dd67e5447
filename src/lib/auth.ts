// import { checkout, polar, webhooks } from '@polar-sh/better-auth'
// import { Polar } from '@polar-sh/sdk'
import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { nextCookies } from 'better-auth/next-js'
import { organization } from 'better-auth/plugins'
import { headers } from 'next/headers'

// import ForgotPasswordEmail from '@/components/emails/forgot-password'
// import db from '@/db/drizzle'
import { db } from '@/lib/db'
import { schema } from '@/db/schema'
import {
  sendEmail,
  createVerificationEmailTemplate,
  createPasswordResetEmailTemplate,
  createWelcomeEmailTemplate,
  createOrganizationInvitationTemplate,
} from '@/lib/email'
import {
  getDisplayName,
  getOrganizationLogo,
  generateOrganizationSlug,
} from '@/lib/email-utils'
import { getActiveOrganizationForUser } from '@/lib/organization-utils'
// import { getPolarProducts } from '@/server/products'
// import { insertPurchase } from '@/server/tokens'

// const polarClient = new Polar({
//   accessToken: process.env.POLAR_ACCESS_TOKEN,
// })

export const auth = betterAuth({
  emailVerification: {
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      const { html, text } = createVerificationEmailTemplate(url, user.name)
      await sendEmail({
        to: user.email,
        subject: 'Verify your email address - Adori AI',
        text,
        html,
      })
    },
    afterEmailVerification: async (user, request) => {
      //Create a new organization for the user
      try {
        console.log(`Organization check: ${user.name}`)
        console.log(`Organization check: ${user.id}`)
        console.log('Organization check:', await request?.headers)
        console.log('Organization check:', await headers())

        const organization = await auth.api.createOrganization({
          body: {
            name: getDisplayName(user.name || '', user.email),
            slug: generateOrganizationSlug(user.name || '', user.email),
            logo: getOrganizationLogo(user.name || '', user.email),
            userId: user.id,
            keepCurrentActiveOrganization: true,
          },
          headers: await headers(),
        })
        console.log(`Organization created: ${organization}`)
      } catch (error) {
        console.error('Failed to create organization:', error)
      }
      // Send welcome email after successful email verification
      try {
        const { html, text } = createWelcomeEmailTemplate(user.name)
        await sendEmail({
          to: user.email,
          subject: 'Welcome to Adori AI! 🎉',
          text,
          html,
        })
        console.log(`Welcome email sent to: ${user.email}`)
      } catch (error) {
        console.error('Failed to send welcome email:', error)
        // Don't throw error to avoid breaking the verification flow
      }
    },
  },

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendResetPassword: async ({ user, url }) => {
      const { html, text } = createPasswordResetEmailTemplate(url, user.name)
      await sendEmail({
        to: user.email,
        subject: 'Reset your password - Adori AI',
        text,
        html,
      })
    },
    onPasswordReset: async ({ user }) => {
      // Log password reset for security monitoring
      console.log(`Password reset completed for user: ${user.email}`)
    },
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  database: drizzleAdapter(db, {
    provider: 'pg',
    schema: schema,
  }),
  databaseHooks: {
    session: {
      create: {
        before: async session => {
          const organization = await getActiveOrganizationForUser(
            session.userId
          )
          return {
            data: {
              ...session,
              activeOrganizationId: organization?.organizationId || null,
            },
          }
        },
      },
    },
  },
  plugins: [
    // polar({
    //   client: polarClient,
    //   createCustomerOnSignUp: true,
    //   use: [
    //     checkout({
    //       products: await getPolarProducts(),
    //       successUrl: '/dashboard/payment/success',
    //       authenticatedUsersOnly: true,
    //     }),
    //     webhooks({
    //       secret: process.env.POLAR_WEBHOOK_SECRET!,
    //       onOrderPaid: async payload => {
    //         if (payload.data.paid) {
    //           insertPurchase(
    //             payload.data.product.id,
    //             payload.data.customer.externalId as string
    //           )
    //         }
    //       },
    //     }),
    //   ],
    // }),
    organization({
      sendInvitationEmail: async data => {
        const inviteLink = `${process.env.NEXT_PUBLIC_APP_URL}/accept-invitation/${data.id}`
        const { html, text } = createOrganizationInvitationTemplate({
          email: data.email,
          invitedByUsername: data.inviter.user.name || data.inviter.user.email,
          invitedByEmail: data.inviter.user.email,
          teamName: data.organization.name,
          inviteLink,
        })
        await sendEmail({
          to: data.email,
          subject: `You've been invited to join ${data.organization.name} on Adori AI`,
          text,
          html,
        })
      },
      organizationCreation: {
        // beforeCreate: async ({ organization, user }) => {
        //   // Use our utility functions for consistent naming and slug generation
        //   const orgName = getDisplayName(user.name || '', user.email)
        //   const orgSlug = generateOrganizationSlug(user.name || '', user.email)
        //   const orgLogo = getOrganizationLogo(user.name || '', user.email)
        //   return {
        //     data: {
        //       ...organization,
        //       name: orgName,
        //       slug: orgSlug,
        //       logo: orgLogo,
        //     },
        //   }
        // },
        // afterCreate: async ({ organization, user }) => {
        //   console.log(
        //     `Organization "${organization.name}" created for user ${user.email}`
        //   )
        // },
      },
    }),
    nextCookies(), //Should be last
  ],
})
