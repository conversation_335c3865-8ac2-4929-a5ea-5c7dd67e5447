import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses'

// Initialize SES client
const sesClient = new SESClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.SES_ACCESS_ID!,
    secretAccessKey: process.env.SES_ACCESS_KEY!,
  },
})

interface SendEmailParams {
  to: string
  subject: string
  text: string
  html?: string
  from?: string
}

export async function sendEmail({
  to,
  subject,
  text,
  html,
  from = process.env.EMAIL_SENDER_ADDRESS || '<EMAIL>',
}: SendEmailParams) {
  // Format the from address with display name like "Adori AI <<EMAIL>>"
  const formattedFrom = from.includes('<') ? from : `Adori AI <${from}>`
  try {
    const command = new SendEmailCommand({
      Source: formattedFrom,
      Destination: {
        ToAddresses: [to],
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Text: {
            Data: text,
            Charset: 'UTF-8',
          },
          ...(html && {
            Html: {
              Data: html,
              Charset: 'UTF-8',
            },
          }),
        },
      },
    })

    const result = await sesClient.send(command)
    console.log('Email sent successfully:', result.MessageId)
    return result
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}

// Email templates
export function createVerificationEmailTemplate(
  url: string,
  userName?: string
) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #FF8C00 0%, #FF1493 50%, #8A2BE2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #8A2BE2; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Adori AI!</h1>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>Thank you for signing up for Adori AI! To complete your registration, please verify your email address by clicking the button below:</p>

          <div style="text-align: center; color: #fff;">
            <a href="${url}" class="button">Verify Email Address</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #FF8C00;">${url}</p>

          <p>This link will expire in 24 hours for security reasons.</p>

          <p>If you didn't create an account with Adori AI, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This email was sent to you because you signed up for Adori AI.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Welcome to Adori AI!

Hi${userName ? ` ${userName}` : ''},

Thank you for signing up for Adori AI! To complete your registration, please verify your email address by clicking the link below:

${url}

This link will expire in 24 hours for security reasons.

If you didn't create an account with Adori AI, you can safely ignore this email.

Best regards,
The Adori AI Team
  `

  return { html, text }
}

export function createWelcomeEmailTemplate(userName?: string) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Adori AI!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #FF8C00 0%, #FF1493 50%, #8A2BE2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #8A2BE2; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #FF8C00; }
        .feature h3 { margin: 0 0 10px 0; color: #FF8C00; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to Adori AI!</h1>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>Welcome to Adori AI! We're thrilled to have you on board. You're now part of a community that's transforming the way people create and share video content.</p>

          <div class="feature">
            <h3>🚀 What You Can Do Now</h3>
            <p>With Adori AI, you can:</p>
            <ul>
              <li>Transform text, blogs, and ideas into engaging videos</li>
              <li>Convert PDFs and podcasts into visual content</li>
              <li>Create professional videos with AI-powered editing</li>
              <li>Generate captions and voiceovers automatically</li>
            </ul>
          </div>

          <div class="feature">
            <h3>🎬 Quick Start Guide</h3>
            <p>Ready to create your first video?</p>
            <ol>
              <li>Upload your content (text, PDF, or podcast)</li>
              <li>Choose your video style and settings</li>
              <li>Let our AI generate your video</li>
              <li>Download and share your creation!</li>
            </ol>
          </div>

          <div style="text-align: center; color: #fff;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://adoriai.com'}" class="button">Start Creating Videos</a>
          </div>

          <p>If you have any questions or need help getting started, don't hesitate to reach out to our support team.</p>

          <p>Happy creating!</p>
          <p><strong>The Adori AI Team</strong></p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This email was sent to you because you signed up for Adori AI.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
🎉 Welcome to Adori AI!

Hi${userName ? ` ${userName}` : ''},

Welcome to Adori AI! We're thrilled to have you on board. You're now part of a community that's transforming the way people create and share video content.

🚀 What You Can Do Now
With Adori AI, you can:
• Transform text, blogs, and ideas into engaging videos
• Convert PDFs and podcasts into visual content
• Create professional videos with AI-powered editing
• Generate captions and voiceovers automatically

🎬 Quick Start Guide
Ready to create your first video?
1. Upload your content (text, PDF, or podcast)
2. Choose your video style and settings
3. Let our AI generate your video
4. Download and share your creation!

Start creating videos at: ${process.env.NEXT_PUBLIC_APP_URL || 'https://app.adoriai.com'}

If you have any questions or need help getting started, don't hesitate to reach out to our support team.

Happy creating!

The Adori AI Team

© 2025 Adori AI. All rights reserved.
  `

  return { html, text }
}

export function createPasswordResetEmailTemplate(
  url: string,
  userName?: string
) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #FF8C00 0%, #FF1493 50%, #8A2BE2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #8A2BE2; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset Request</h1>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>We received a request to reset your password for your Adori AI account. Click the button below to create a new password:</p>

          <div style="text-align: center;">
            <a href="${url}" class="button">Reset Password</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #FF8C00;">${url}</p>

          <div class="warning">
            <strong>Security Notice:</strong> This link will expire in 1 hour for security reasons. If you didn't request a password reset, please ignore this email and your password will remain unchanged.
          </div>

          <p>If you didn't request this password reset, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This email was sent to you because you requested a password reset for your Adori AI account.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Password Reset Request - Adori AI

Hi${userName ? ` ${userName}` : ''},

We received a request to reset your password for your Adori AI account. Click the link below to create a new password:

${url}

SECURITY NOTICE: This link will expire in 1 hour for security reasons. If you didn't request a password reset, please ignore this email and your password will remain unchanged.

If you didn't request this password reset, you can safely ignore this email.

Best regards,
The Adori AI Team
  `

  return { html, text }
}

export function createOrganizationInvitationTemplate({
  email,
  invitedByUsername,
  invitedByEmail,
  teamName,
  inviteLink,
}: {
  email: string
  invitedByUsername: string
  invitedByEmail: string
  teamName: string
  inviteLink: string
}) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>You've been invited to join ${teamName} - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #FF8C00 0%, #FF1493 50%, #8A2BE2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #8A2BE2; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .highlight { background: #e8f4fd; border-left: 4px solid #FF8C00; padding: 15px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>You're Invited!</h1>
        </div>
        <div class="content">
          <h2>Hi there,</h2>
          <p><strong>${invitedByUsername}</strong> (${invitedByEmail}) has invited you to join <strong>${teamName}</strong> on Adori AI!</p>

          <div class="highlight">
            <p><strong>What is Adori AI?</strong></p>
            <p>Adori AI is a powerful platform that transforms your content into engaging videos using artificial intelligence. Create professional videos from text, blogs, PDFs, and podcasts in minutes!</p>
          </div>

          <p>As a member of ${teamName}, you'll be able to:</p>
          <ul>
            <li>Create and collaborate on video projects</li>
            <li>Access shared resources and templates</li>
            <li>Work with your team on content creation</li>
            <li>Manage projects and workflows together</li>
          </ul>

          <div style="text-align: center; color: #fff;">
            <a href="${inviteLink}" class="button">Accept Invitation</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #FF8C00;">${inviteLink}</p>

          <p><strong>Note:</strong> This invitation link will expire in 48 hours for security reasons.</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This invitation was sent to ${email} by ${invitedByEmail}.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
You've been invited to join ${teamName} - Adori AI

Hi there,

${invitedByUsername} (${invitedByEmail}) has invited you to join ${teamName} on Adori AI!

What is Adori AI?
Adori AI is a powerful platform that transforms your content into engaging videos using artificial intelligence. Create professional videos from text, blogs, PDFs, and podcasts in minutes!

As a member of ${teamName}, you'll be able to:
• Create and collaborate on video projects
• Access shared resources and templates
• Work with your team on content creation
• Manage projects and workflows together

Accept your invitation here: ${inviteLink}

Note: This invitation link will expire in 48 hours for security reasons.

Best regards,
The Adori AI Team

© 2025 Adori AI. All rights reserved.
This invitation was sent to ${email} by ${invitedByEmail}.
  `

  return { html, text }
}
