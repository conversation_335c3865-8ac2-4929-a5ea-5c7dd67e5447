'use server'

import { eq } from 'drizzle-orm'
import { headers } from 'next/headers'
import { redirect } from 'next/navigation'

// import db from '@/db/drizzle'
import { db } from '@/lib/db'
import { user } from '@/db/schema'
import { auth } from '@/lib/auth'

export const getUserById = async (id: string) => {
  const [currentUser] = await db.select().from(user).where(eq(user.id, id))

  return currentUser
}

export const getUserByEmail = async (email: string) => {
  const [currentUser] = await db
    .select()
    .from(user)
    .where(eq(user.email, email))

  return currentUser
}

export const getUserSession = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  })

  if (!session?.user?.id) {
    redirect('/signin')
  }

  const currentUser = await db.query.user.findFirst({
    where: eq(user.id, session?.user?.id),
  })

  if (!currentUser) {
    redirect('/signin')
  }

  return {
    ...session,
    user: currentUser,
  }
}

export const signInUser = async (
  _: unknown,
  formData: FormData
): Promise<{
  errors: Record<string, string[]>
  values: Record<string, string>
  redirect?: string
}> => {
  const formValues = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  // Server-side password validation to prevent long password DoS attacks
  const errors: Record<string, string[]> = {}

  if (!formValues.password) {
    errors.password = ['Password is required']
  } else if (formValues.password.length < 8) {
    errors.password = ['Password must be at least 8 characters long']
  } else if (formValues.password.length > 48) {
    errors.password = ['Password must be no more than 48 characters long']
  }

  if (Object.keys(errors).length > 0) {
    return {
      errors,
      values: {},
    }
  }

  try {
    await auth.api.signInEmail({
      body: {
        email: formValues.email,
        password: formValues.password,
      },
    })

    return {
      errors: {},
      values: {
        text: 'Successfully signed in.',
      },
      redirect: '/',
    }
  } catch (e: unknown) {
    const error = e as Error
    return {
      errors: { message: [error.message || 'An unknown error occurred'] },
      values: {},
    }
  }
}

export const signUpUser = async (
  _: unknown,
  formData: FormData
): Promise<{
  errors: Record<string, string[]>
  values: Record<string, string>
  redirect?: string
}> => {
  const formValues = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
    name: formData.get('name') as string,
  }

  // Server-side password validation to prevent long password DoS attacks
  const errors: Record<string, string[]> = {}

  if (!formValues.password) {
    errors.password = ['Password is required']
  } else if (formValues.password.length < 8) {
    errors.password = ['Password must be at least 8 characters long']
  } else if (formValues.password.length > 48) {
    errors.password = ['Password must be no more than 48 characters long']
  }

  if (Object.keys(errors).length > 0) {
    return {
      errors,
      values: {},
    }
  }

  try {
    await auth.api.signUpEmail({
      body: {
        email: formValues.email,
        password: formValues.password,
        name: formValues.name,
      },
    })

    return {
      errors: {},
      values: {
        text: 'Successfully signed up.',
      },
      redirect: '/',
    }
  } catch (e) {
    const error = e as Error
    return {
      errors: { message: [error.message || 'An unknown error occurred'] },
      values: {},
    }
  }
}

export const getUserProfile = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  })

  if (!session?.user?.id) {
    throw new Error('User not found')
  }

  const [userProfile] = await db
    .select()
    .from(user)
    .where(eq(user.id, session?.user?.id))

  return userProfile
}

export const updateProfile = async (
  data: Partial<typeof user.$inferInsert>
) => {
  const session = await getUserSession()

  try {
    await db.update(user).set(data).where(eq(user.id, session?.user?.id))

    return {
      values: {
        text: 'Successfully updated profile.',
      },
      redirect: '/',
    }
  } catch (e) {
    const error = e as Error
    return {
      errors: { message: [error.message || 'An unknown error occurred'] },
    }
  }
}
